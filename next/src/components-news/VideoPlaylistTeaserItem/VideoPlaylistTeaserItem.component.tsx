import Image from 'next/image'
import Link from 'next/link'
import type { VideoSnippet } from '~/src/generated'
import { teaserTimestamp } from '~/src/utils/teaser-timestamp'
import { CategoryTeaserLink } from '../CategoryTeaserLink/CategoryTeaserLink.component'

const bucket = 'https://storage.googleapis.com/kitco-video-dev'

export function VideoPlaylistTeaserItem({
  video,
  hidePlaylistLink,
}: {
  video: VideoSnippet
  hidePlaylistLink?: boolean
}) {
  return (
    <div className="relative flex flex-col">
      <div className="relative mb-2 flex aspect-video w-full">
        <Link href={video?.frontendPath || '/'} className="">
          <Image
            src={`${bucket}/${video?.uuid}/${video?.thumbnailUuid}_poster.jpeg`}
            alt={`${video?.headline} teaser image`}
            fill
            priority={true}
            className="relative aspect-video h-full w-full rounded-md"
          />
        </Link>
      </div>
      {hidePlaylistLink ? null : (
        <CategoryTeaserLink urlAlias="/" text={video?.category?.name} />
      )}

      <Link href={video?.frontendPath || '/'} className="relative">
        <h3 className="text-base sm:!text-white">{video?.headline}</h3>
        <time
          className="text-white opacity-60"
          dateTime={teaserTimestamp(video?.createdAt)}
        >
          {teaserTimestamp(video?.createdAt)}
        </time>
      </Link>
    </div>
  )
}
