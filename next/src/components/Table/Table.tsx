import Link from 'next/link'
import type { FC } from 'react'
import st from './Table.module.scss'

interface TableProps {
  title: string
  /** Children are intended to be TableItems*/
  children: React.ReactNode
  href?: string
}

const Table: FC<TableProps> = ({
  title,
  children,
  href = null,
}: TableProps) => {
  return (
    <div>
      <header className={st.head}>
        <h2 className={st.txt}>
          {href ? (
            <Link className="text-white" href={href}>
              {title}
            </Link>
          ) : (
            <>{title}</>
          )}
        </h2>
      </header>

      <div className="border">{children}</div>
    </div>
  )
}

export default Table
