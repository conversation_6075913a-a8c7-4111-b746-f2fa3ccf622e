@import '../../styles/vars';

// deprecated
// div.contentWrapper {
//   border: solid 1px $dark-grey;
//   border-top: 0;
//   padding: 10px;
// }

.videoContainer {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px 10px;
}

@media screen and (max-width: 767px) {
  .videoContainer {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px 10px;
  }
}

.videoImage {
  max-width: 230px;
  min-width: 130px;
  width: 100%;
  height: auto;
  aspect-ratio: 16/9;
}

img.mainImg {
  max-width: 100%;
}

div.gridTwoColumn {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  column-gap: 10px;
  padding-top: 10px;
}

img.lilImg {
  max-width: 100%;
}

button.moreVideosBtn {
  margin-top: 10px;
  width: 100%;
  padding: 0.4em 0;
  text-align: center;
  color: white;
  font-size: 14px;
  font-weight: 600;
  border-radius: 2px;
}

// is loading
div.contentWrapperLoading {
  border: solid 1px $dark-grey;
  border-top: 0;
  padding: 10px;
}

.firstVideoContainerLoading {
  padding-bottom: 10px;
  border-bottom: solid 1px $dark-grey;
}

div.mainImgLoading {
  width: 100%;
  height: 150px;
  background-color: $light-grey;
}

div.gridTwoColumnLoading {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  column-gap: 10px;
  padding-top: 10px;
}

div.lilImgLoading {
  width: 100%;
  height: 60px;
  background-color: $light-grey;
}

div.loadingText {
  height: 16px;
  width: 80%;
  background-color: $light-grey;
  margin: 4px 0;
}

div.loadingTextTwo {
  height: 16px;
  width: 60%;
  background-color: $light-grey;
  margin: 4px 0;
}
