import Link from 'next/link'
import { type ReactNode, useState } from 'react'
import { BiChevronDown, BiChevronUp } from 'react-icons/bi'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import fontStyles from '~/src/styles/news-typefaces.module.scss'
import cs from '~/src/utils/cs'
import css from './static-opinions-landing-nav.module.scss'

export function StaticOpinionsLandingNavigationMobile() {
  const [isOpen, setIsOpen] = useState(false)
  const { data } = kitcoQuery(news.newsCategoriesTree({}))
  const iconSection = (): ReactNode => {
    if (!isOpen) {
      return <BiChevronDown color="#2494D6" size={20} />
    }

    return <BiChevronUp color="#2494D6" size={20} />
  }

  return (
    <div className={cs([css.wrapper, 'mb-4'])}>
      <div className="flex items-center justify-between">
        <h1 className="text-[32px] uppercase md:text-[48px]">Opinion</h1>
        <button
          type="button"
          className="flex items-center"
          onClick={() => setIsOpen(!isOpen)}
        >
          <span className={cs([fontStyles.section, 'mr-2'])}>Sections</span>
          {iconSection()}
        </button>
      </div>
      {isOpen && (
        <ul className="absolute left-0 z-50 w-full bg-kitco-black p-4">
          {data?.categoriesTree?.map((x) => (
            <li
              key={x.id}
              className="relative border-b border-neutral-600 px-4 py-2 last:border-b-0"
            >
              <Link
                href={x.urlAlias}
                className="text-[16px] text-white"
                onClick={() => setIsOpen(false)}
              >
                <span>{x.name}</span>
              </Link>
              <ul className="flex list-disc flex-col">
                {x?.children?.length > 0 &&
                  x?.children?.map((y) => (
                    <li key={`${x.id}${x.name}`}>
                      <Link
                        href={y.urlAlias}
                        className="pl-6 text-white"
                        onClick={() => setIsOpen(false)}
                      >
                        <span>{y?.name}</span>
                      </Link>
                    </li>
                  ))}
              </ul>
            </li>
          ))}
        </ul>
      )}
    </div>
  )
}
