import classNames from 'classnames'
import clsx from 'clsx'
import type { FC, ReactNode } from 'react'
import BlockShell from '~/src/components/BlockShell/BlockShell'
import styles from '~/src/components/MetalMonthHomePage/MetalMonthHomePage.module.scss'
import dates from '~/src/utils/dates'
import { useGetMarketStatus } from '~/src/utils/market-status.util'

/**
 * MetalMonthShell component props
 *
 * @interface MetalMonthShellProps
 * @property {ReactNode} children - The children components.
 */
interface MetalMonthShellProps {
  children: ReactNode
}

/**
 * MetalMonthShell component
 *
 * @param {ReactNode} children - The children components.
 * @returns {ReactElement} - The rendered component.
 */
export const MetalMonthShell: FC<MetalMonthShellProps> = ({
  children,
}: MetalMonthShellProps) => {
  const { data, statusString, timeToNextStatusString } = useGetMarketStatus()
  const classes = classNames(
    styles.upper,
    data?.GetMarketStatus?.status === 'OPEN' && styles.up,
    data?.GetMarketStatus?.status === 'CLOSED' && styles.down,
  )
  return (
    <div className={styles.wrap}>
      <div className={clsx(styles.market, 'block tablet:hidden')}>
        <div className={classNames(classes)}>
          SPOT {statusString}{' '}
          <small className="text-black">({timeToNextStatusString})</small>
        </div>
        <div>
          <small className={styles.bold} suppressHydrationWarning>
            {dates.dayTime()} NY Time
          </small>
        </div>
      </div>
      <BlockShell title="Live Spot Gold" href="/charts/gold">
        <div className={clsx(styles.market, 'hidden desktop:block')}>
          <div className={classNames(classes)}>SPOT {statusString}</div>
          <div>
            <small>{timeToNextStatusString}</small>
          </div>
          <div>
            <small className={styles.bold} suppressHydrationWarning>
              {dates.dayTime()} NY Time
            </small>
          </div>
        </div>
        {children}
      </BlockShell>
    </div>
  )
}

export default MetalMonthShell
