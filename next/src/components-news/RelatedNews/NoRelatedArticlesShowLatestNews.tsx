import { Fragment } from 'react'
import { TeaserCard } from '~/src/components-news/ArticleTeasers/TeaserCard'
import { TeaserCardMobile } from '~/src/components-news/ArticleTeasers/TeaserCardMobile'
import type {
  ArticleTeaserFragmentFragment,
  NodeListQueueQuery,
} from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'

export function NoRelatedArticlesShowLatestNews(props: {
  currentNodeId: number
  classTeaserCard: string
}) {
  // NOTE: trending is not a query, it's preserved state from the ssr query
  const { data } = kitcoQuery(
    news.nodeListQueue({
      variables: {
        queueId: 'latest_news',
        limit: 5,
        offset: 0,
      },
      options: {
        enabled: true,
        select: (d: NodeListQueueQuery) => {
          const filterItems = d?.queue?.items
            ?.filter((x) => x?.__typename !== 'Commentary')
            ?.filter(
              (x: ArticleTeaserFragmentFragment) =>
                x.id !== props.currentNodeId,
            )
            .slice(0, 3)

          // this mostly just preserves the return type of the query
          const preserveDataModel = { ...d?.queue, items: filterItems }

          return { ...d, nodeListQueue: preserveDataModel }
        },
      },
    }),
  )

  return (
    <div className="grid-row-3 md:grid-row-1 grid grid-cols-1 gap-10 md:grid-cols-3">
      {data?.queue?.items?.map((article: ArticleTeaserFragmentFragment) => {
        return (
          <Fragment key={article.id}>
            <TeaserCard
              node={article}
              size="sm"
              classWrapper="hidden md:block"
            />
            <TeaserCardMobile
              node={article}
              size="sm"
              classWrapper="md:hidden"
            />
          </Fragment>
        )
      })}
    </div>
  )
}
