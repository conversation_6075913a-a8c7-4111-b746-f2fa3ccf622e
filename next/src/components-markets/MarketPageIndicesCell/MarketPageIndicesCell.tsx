import type { FC } from 'react'
import QuotesTable from '~/src/components/QuotesTable/QuotesTable'
import { markets } from '~/src/lib/markets-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import * as timestamps from '~/src/utils/timestamps'

export const indicesSymbols = '$DOWI,$NASX,$SPX,$NYA'
export const indices = [
  { symbol: '$DOWI', name: '<PERSON> Jones' },
  { symbol: '$NASX', name: 'Nasdaq' },
  { symbol: '$SPX', name: 'S&P 500' },
  { symbol: '$NYA', name: 'NYSE' },
]

const MarketPageIndicesCell: FC = () => {
  const { data, isFetching } = kitcoQuery(
    markets.barchartsQuotes({
      variables: {
        symbols: '$DOWI,$NASX,$SPX,$NYA',
        timestamp: timestamps.current(),
      },
    }),
  )

  return (
    <QuotesTable
      title="Market Indices"
      section="indices"
      data={data?.GetBarchartQuotes?.results}
      isLoading={isFetching}
    />
  )
}
export default MarketPageIndicesCell
