.section {
  position: relative;
  max-width: 100%;

  @media only screen and (max-width: 768px) {
    width: 100%;
  }
}

.sectionTitle {
  font-size: 2em;
  line-height: 1.2em;
  font-family: 'Lato', sans-serif;
  font-weight: 600;

  @media only screen and (max-width: 768px) {
    font-size: 1.875em;
  }
}

.grid {
  display: grid;
  grid-template-columns: 35% 1fr;
  margin-top: 1em;

  @media only screen and (max-width: 768px) {
    grid-template-columns: 1fr;
    max-width: 100%;
  }
}

.leftColumn {
  padding-right: 1em;
  border-right: solid 1px #e5e5e5;
  word-wrap: wrap;

  @media only screen and (max-width: 768px) {
    max-width: 100%;
    position: relative;
    border-right: 0;
    padding: 0;

    & p {
      max-width: 100%;
    }
  }
}

.rightColumn {
  position: relative;
  max-width: 100%;
  padding-left: 1em;

  @media only screen and (max-width: 768px) {
    grid-template-columns: 1fr;
    padding-left: 0;
    max-width: 100%;
  }
}

.title {
  font-family: Lato, sans-serif;
  font-weight: 700;
  color: #373737;
  overflow: hidden;
  display: -webkit-box;
  line-height: 130%;
  font-size: 16px;
  margin-top: 4px;
  padding: 0;
}

.date {
  margin-bottom: 7px;
  font-family: 'Mulish', sans-serif;
  --tw-text-opacity: 1;
  color: rgb(117 117 117 / var(--tw-text-opacity));
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.lineTitle {
  margin: 15px 0;
  width: 100%;
  height: 3px;
  background-color: #e2e1e1;
}

.bottomPanel {
  border-top: 1px solid #e2e1e1;
  margin: 14px 0;
  clear: both;
}
