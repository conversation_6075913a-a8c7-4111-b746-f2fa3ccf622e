import { type FC, Suspense } from 'react'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import OtherIndicators from '~/src/components/OtherIndicators/OtherIndicators'
import { Query } from '~/src/components/Query/Query'
import { markets } from '~/src/lib/markets-factory.lib'
import * as timestamps from '~/src/utils/timestamps'

const OtherIndicesCell: FC = () => {
  const fetcher = markets.barchartsQuotes({
    variables: {
      timestamp: timestamps.current(),
      symbols: '$DOWI,$NASX,$SPX,$NYA,$DXY,$NKY,$TXCX,$DXY',
    },
  })

  return (
    <ErrBoundary>
      <Suspense fallback={<div>Loading...</div>}>
        <Query fetcher={fetcher}>
          {({ data }) => {
            return (
              <div>
                <OtherIndicators title="Other indicators" data={data} />
              </div>
            )
          }}
        </Query>
      </Suspense>
    </ErrBoundary>
  )
}
export default OtherIndicesCell
