import type { FC } from 'react'
import styles from './MarketIndices.module.scss'

type Region = 'US' | 'EU' | 'ASIA'
type TabsProps = {
  region: Region
  setRegion: (region: Region) => void
}

const Tabs: FC<TabsProps> = ({ region, setRegion }) => {
  const getClass = (tabRegion: Region) =>
    region === tabRegion ? styles.tabButtonActive : styles.tabButton

  return (
    <div className={styles.tabsGrid}>
      <button
        type="button"
        className={getClass('US')}
        onClick={() => setRegion('US')}
      >
        US
      </button>
      <button
        type="button"
        className={getClass('EU')}
        onClick={() => setRegion('EU')}
      >
        EUROPE
      </button>
      <button
        type="button"
        className={getClass('ASIA')}
        onClick={() => setRegion('ASIA')}
      >
        ASIA
      </button>
    </div>
  )
}

export default Tabs
