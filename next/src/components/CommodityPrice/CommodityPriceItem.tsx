import type { FC } from 'react'
import Change from '~/src/components/CommodityPrice/Change'
import styles from '~/src/components/CommodityPrice/CommodityPrice.module.scss'
import Price from '~/src/components/CommodityPrice/Price'
import Row from '~/src/components/CommodityPrice/Row'
import RowLabel from '~/src/components/CommodityPrice/RowLabel'

interface CommodityPriceItemProps {
  label: string
  value: string
  change: string
  isUp: boolean
}

const CommodityPriceItem: FC<CommodityPriceItemProps> = ({
  label,
  value,
  change,
  isUp,
}: CommodityPriceItemProps) => {
  return (
    <Row>
      <RowLabel>{label}</RowLabel>
      <Price>{value}</Price>
      <Change styleUpOrDown={isUp ? styles.up : styles.down}>
        {isUp && '+'}
        {change}
      </Change>
    </Row>
  )
}

export default CommodityPriceItem
