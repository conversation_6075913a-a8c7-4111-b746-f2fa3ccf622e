import clsx from 'clsx'
import Image from 'next/image'
import { useRef, useState } from 'react'
import { env } from '~/src/env/client.mjs'
import styles from './VideoPlayer.module.scss'

type TVideoNode = {
  overlayImageUrl?: string
  assetUuid: string
  startTime: number
  endTime: number
  snippetUuid: string
  thumbnailUuid: string
  assetType: string
}

const VideoPlayer = ({ videoNode }: { videoNode: TVideoNode }) => {
  const videoRef = useRef(null)
  const [isPaused, setIsPaused] = useState(true)

  const handlePlay = () => {
    setIsPaused(false)
  }

  const handlePause = () => {
    setIsPaused(true)
  }

  const handleVideoMount = (element) => {
    if (element) {
      element.addEventListener('play', handlePlay)
      element.addEventListener('pause', handlePause)
    }
  }

  const posterUrl = !videoNode.overlayImageUrl
    ? `${env.NEXT_PUBLIC_VCMS_BUCKET}/${videoNode.snippetUuid}/${videoNode.thumbnailUuid}_poster.jpeg`
    : videoNode.overlayImageUrl

  if (videoNode.assetUuid && videoNode.assetType === 'audio') {
    return (
      <div className={styles.containerAudio}>
        <audio
          id={videoNode.snippetUuid}
          className="video-js videoPlayerEmbedded"
          controls
          preload="auto"
          style={{ backgroundColor: 'transparent' }}
        >
          <source
            src={`${env.NEXT_PUBLIC_ACMS_BUCKET}/${videoNode.assetUuid}/asset.m4a`}
            type="audio/mp4"
          />
          <p className="vjs-no-js">
            To listen to this audio please enable JavaScript
          </p>
        </audio>
      </div>
    )
  }

  return (
    <div className={clsx('relative aspect-video')}>
      {videoNode.assetUuid && videoNode.assetType === 'video' ? (
        <>
          <video
            ref={(element) => {
              videoRef.current = element
              handleVideoMount(element)
            }}
            id={videoNode.snippetUuid}
            className="video-js video-js-default videoPlayerEmbedded"
            controls
            preload="auto"
            poster={posterUrl}
          >
            <source
              src={`${env.NEXT_PUBLIC_VCMS_BUCKET}/${videoNode.assetUuid}/video.mp4`}
              type="video/mp4"
            />
            <p className="vjs-no-js">
              To view this video please enable JavaScript
            </p>
          </video>
          <div className="pointer-events-none absolute inset-0 flex items-center justify-center">
            {isPaused && (
              <Image
                width={50}
                height={50}
                src={'/bi_play-circle-fill.svg'}
                alt="circle-play-icon"
              />
            )}
          </div>
        </>
      ) : (
        <div
          className={clsx('aspect-video h-full w-full', 'animate-loading')}
        />
      )}
    </div>
  )
}

export default VideoPlayer
