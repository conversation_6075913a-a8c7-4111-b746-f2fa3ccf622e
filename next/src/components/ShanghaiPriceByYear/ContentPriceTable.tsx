import { CurrencySelectCNY } from '~/src/components/CurrencySelect'
import { ColumnTitles } from '~/src/components/ShanghaiPriceByYear/ColumnTitles'
import styles from '~/src/components/ShanghaiPriceByYear/ShanghaiPriceByYear.module.scss'
import { Values } from '~/src/components/ShanghaiPriceByYear/Values'
import Table from '~/src/components/Table/Table'
import { DynamicWeightSelect } from '~/src/components/WeightSelect'
import { TimeSelect } from '~/src/components/year-select/year-select.component'
import useWeight from '~/src/hooks/Weight/useWeight'
import WeightType from '~/src/types/WeightSelect/WeightType'
import cs from '~/src/utils/cs'

export const ContentPriceTable = ({ isFetching, results }) => {
  const weight = useWeight(WeightType.PreciousMetals, 'GRAM', 'shanghai')
  return (
    <>
      <div className="mb-2 flex items-center justify-end">
        <CurrencySelectCNY classNamesListbox={styles.listbox} />
        <div className={cs(['pl-4'])}>
          <DynamicWeightSelect
            type={WeightType.PreciousMetals}
            defaultWeight="GRAM"
            id="shanghai"
          />
        </div>
        <div className={cs(['pl-4'])}>
          <TimeSelect styleSelect={styles.selectStyle} />
        </div>
      </div>
      <Table title="Shanghai Fix Latest Price">
        <ColumnTitles />
        <Values
          timestamp={results?.[0]?.timestamp}
          am={results?.[0]?.am}
          pm={results?.[0]?.pm}
          idx={0}
          isLoading={isFetching}
          weight={weight}
        />
      </Table>
      <div className="mt-10" />
      <Table title="Shanghai Fix Historical Prices">
        <div className="no-scrollbar overflow-hidden overflow-x-scroll">
          {/* <div className="min-w-[700px]"> */}
          <ColumnTitles />
          <div className="h-[350px] overflow-x-hidden">
            {results.length > 0 ? (
              <>
                {results.slice(1).map(({ timestamp, am, pm }, idx: number) => (
                  <Values
                    timestamp={timestamp}
                    am={am}
                    pm={pm}
                    key={idx}
                    idx={idx}
                    isLoading={isFetching}
                    weight={weight}
                  />
                ))}
              </>
            ) : isFetching ? (
              <>
                {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((_) => (
                  <Values
                    timestamp={undefined}
                    am={undefined}
                    pm={undefined}
                    idx={undefined}
                    key={weight.label}
                    isLoading={isFetching}
                    weight={weight}
                  />
                ))}
              </>
            ) : (
              <div className="text-center">No data</div>
            )}
          </div>
          {/* </div> */}
        </div>
      </Table>
    </>
  )
}
