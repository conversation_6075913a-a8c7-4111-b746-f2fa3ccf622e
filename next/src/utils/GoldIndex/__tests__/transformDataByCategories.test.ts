import { transformDataByCategories } from '../transformDataByCategories'
import type CommodityData from '~/src/types/DataTable/CommodityData'

describe('transformDataByCategories', () => {
  const mockCommodityData: CommodityData[] = [
    {
      commodity: 'Gold',
      lastBid: {
        bid: '2000.00',
        bidVal: 2000,
        currency: 'USD',
        originalTime: '2023-01-01T00:00:00Z',
      },
      changeDueToUSD: {
        change: '10.00',
        changeVal: 10,
        percentage: '0.5',
        percentageVal: 0.5,
      },
      changeDueToTrade: {
        change: '5.00',
        changeVal: 5,
        percentage: '0.25',
        percentageVal: 0.25,
      },
      totalChange: {
        change: '15.00',
        changeVal: 15,
        percentage: '0.75',
        percentageVal: 0.75,
      },
    },
    {
      commodity: 'Bitcoin',
      lastBid: {
        bid: '50000.00',
        bidVal: 50000,
        currency: 'USD',
        originalTime: '2023-01-01T00:00:00Z',
      },
      changeDueToUSD: {
        change: '1000.00',
        changeVal: 1000,
        percentage: '2.0',
        percentageVal: 2.0,
      },
      changeDueToTrade: {
        change: '500.00',
        changeVal: 500,
        percentage: '1.0',
        percentageVal: 1.0,
      },
      totalChange: {
        change: '1500.00',
        changeVal: 1500,
        percentage: '3.0',
        percentageVal: 3.0,
      },
    },
    {
      commodity: 'Copper',
      lastBid: {
        bid: '4.50',
        bidVal: 4.5,
        currency: 'USD',
        originalTime: '2023-01-01T00:00:00Z',
      },
      changeDueToUSD: {
        change: '0.10',
        changeVal: 0.1,
        percentage: '2.2',
        percentageVal: 2.2,
      },
      changeDueToTrade: {
        change: '0.05',
        changeVal: 0.05,
        percentage: '1.1',
        percentageVal: 1.1,
      },
      totalChange: {
        change: '0.15',
        changeVal: 0.15,
        percentage: '3.3',
        percentageVal: 3.3,
      },
    },
  ]

  it('should transform data with category headers in correct order', () => {
    const result = transformDataByCategories(mockCommodityData)
    
    // Should have category headers + data
    expect(result.length).toBeGreaterThan(mockCommodityData.length)
    
    // Check that category headers are inserted
    const categoryHeaders = result.filter(item => 
      'type' in item && item.type === 'categoryHeader'
    )
    
    expect(categoryHeaders).toHaveLength(3) // PRECIOUS METALS, CRYPTOCURRENCIES, BASE METALS
    
    // Check order: PRECIOUS METALS first
    expect(result[0]).toMatchObject({
      type: 'categoryHeader',
      categoryName: 'PRECIOUS METALS',
      categoryKey: 'PRECIOUS METALS',
    })
    
    // Gold should follow precious metals header
    expect(result[1].commodity).toBe('Gold')
    
    // Find cryptocurrencies header
    const cryptoHeaderIndex = result.findIndex(item => 
      'type' in item && item.type === 'categoryHeader' && 
      (item as any).categoryName === 'CRYPTOCURRENCIES'
    )
    expect(cryptoHeaderIndex).toBeGreaterThan(0)
    expect(result[cryptoHeaderIndex + 1].commodity).toBe('Bitcoin')
  })

  it('should only include categories that have data', () => {
    const goldOnlyData = mockCommodityData.filter(item => item.commodity === 'Gold')
    const result = transformDataByCategories(goldOnlyData)
    
    const categoryHeaders = result.filter(item => 
      'type' in item && item.type === 'categoryHeader'
    )
    
    expect(categoryHeaders).toHaveLength(1) // Only PRECIOUS METALS
    expect(categoryHeaders[0]).toMatchObject({
      categoryName: 'PRECIOUS METALS',
    })
  })
})
