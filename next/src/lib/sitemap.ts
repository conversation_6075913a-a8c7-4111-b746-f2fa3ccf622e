import type { UseQueryOptions } from '@tanstack/react-query'
import { gql } from 'graphql-request'
import type {
  GetAllAuthorsQuery,
  GetAllAuthorsQueryVariables,
  GetAllCategoriesQuery,
  GetAllCategoriesQueryVariables,
  GetAllLeadGenQuery,
  GetAllLeadGenQueryVariables,
  GetAllNewsArticlesQuery,
  GetAllNewsArticlesQueryVariables,
  GetAllOffTheWireQuery,
  GetAllOffTheWireQueryVariables,
  GetAllOpinionsQuery,
  GetAllOpinionsQueryVariables,
  GetAllTagsQuery,
  GetAllTagsQueryVariables,
  GetAllVideosQuery,
  GetAllVideosQueryVariables,
  VideoSnippetByUuidQuery,
  VideoSnippetByUuidQueryVariables,
} from '~/src/generated'
import { graphs } from '../services/database/fetcher'
import type QueryArgs from '../types/QueryArgs'

/**
 * Sitemap queries.
 *
 * As we only need to fetch the URLs for the sitemap,
 * we can improve the performance by using a custom query.
 */
export const sitemap = {
  getAllAuthors: (
    args?: QueryArgs<GetAllAuthorsQueryVariables, GetAllAuthorsQuery>,
  ): UseQueryOptions<GetAllAuthorsQuery> => {
    return {
      ...args?.options,
      queryKey: ['Sitemap.GetAllAuthors', args?.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query GetAllAuthors {
              reporters {
                urlAlias
                hidden
              }
            }
          `,
          args?.variables,
        ),
    }
  },
  getAllCategories: (
    args?: QueryArgs<GetAllCategoriesQueryVariables, GetAllCategoriesQuery>,
  ): UseQueryOptions<GetAllCategoriesQuery> => {
    return {
      ...args?.options,
      queryKey: ['Sitemap.GetAllCategories', args?.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query GetAllCategories {
              categoriesTree {
                urlAlias
                children {
                  urlAlias
                  children {
                    urlAlias
                    children {
                      urlAlias
                    }
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },
  getAllLeadGen: (
    args?: QueryArgs<GetAllLeadGenQueryVariables, GetAllLeadGenQuery>,
  ): UseQueryOptions<GetAllLeadGenQuery> => {
    return {
      ...args?.options,
      queryKey: ['Sitemap.GetAllLeadGen', args?.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query GetAllLeadGen {
              queue: nodeListQueue(bundles: [LeadGen], queueId: "leadgen") {
                items {
                  ... on LeadGen {
                    urlAlias
                    updatedAt
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },
  getAllNewsArticles: (
    args?: QueryArgs<GetAllNewsArticlesQueryVariables, GetAllNewsArticlesQuery>,
  ): UseQueryOptions<GetAllNewsArticlesQuery> => {
    return {
      ...args?.options,
      queryKey: ['Sitemap.GetAllNewsArticles', args?.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query GetAllNewsArticles($limit: Int, $offset: Int) {
              nodeList(bundles: [NewsArticle], limit: $limit, offset: $offset) {
                items {
                  ... on NewsArticle {
                    urlAlias
                    updatedAt
                    title
                  }
                }
              }
            }
          `,
          {
            limit: args?.variables?.limit || 100, // Default limit to 100 if not specified
            offset: args?.variables?.offset || 0, // Default offset to 0 if not specified
          },
        ),
    }
  },
  getAllOffTheWire: (
    args?: QueryArgs<GetAllOffTheWireQueryVariables, GetAllOffTheWireQuery>,
  ): UseQueryOptions<GetAllOffTheWireQuery> => {
    return {
      ...args?.options,
      queryKey: ['Sitemap.GetAllOffTheWire', args?.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query GetAllOffTheWire($limit: Int, $offset: Int) {
              nodeList(bundles: [OffTheWire], limit: $limit, offset: $offset) {
                items {
                  ... on OffTheWire {
                    urlAlias
                    updatedAt
                    title
                  }
                }
              }
            }
          `,
          {
            limit: args?.variables?.limit || 100, // Default limit to 100 if not specified
            offset: args?.variables?.offset || 0, // Default offset to 0 if not specified
          },
        ),
    }
  },
  getAllOpinions: (
    args?: QueryArgs<GetAllOpinionsQueryVariables, GetAllOpinionsQuery>,
  ): UseQueryOptions<GetAllOpinionsQuery> => {
    return {
      ...args?.options,
      queryKey: ['Sitemap.GetAllOpinions', args?.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query GetAllOpinions($limit: Int, $offset: Int) {
              nodeList(bundles: [Commentary], limit: $limit, offset: $offset) {
                items {
                  ... on Commentary {
                    urlAlias
                    updatedAt
                    title
                  }
                }
              }
            }
          `,
          {
            limit: args?.variables?.limit || 100, // Default limit to 100 if not specified
            offset: args?.variables?.offset || 0, // Default offset to 0 if not specified
          },
        ),
    }
  },
  getAllTags: (
    args?: QueryArgs<GetAllTagsQueryVariables, GetAllTagsQuery>,
  ): UseQueryOptions<GetAllTagsQuery> => {
    return {
      ...args?.options,
      queryKey: ['Sitemap.GetAllTags', args?.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query GetAllTags {
              trendingTags {
                urlAlias
              }
            }
          `,
          args?.variables || {},
        ),
    }
  },
  getAllVideos: (
    args?: QueryArgs<GetAllVideosQueryVariables, GetAllVideosQuery>,
  ): UseQueryOptions<GetAllVideosQuery> => {
    return {
      ...args?.options,
      queryKey: ['Sitemap.GetAllVideos', args?.variables],
      queryFn: async () =>
        await graphs.videosPricesFetch(
          gql`
            query GetAllVideos($limit: Int!, $offset: Int!) {
              VideoLatestVideos(limit: $limit, offset: $offset) {
                snippets {
                  thumbnailUuid
                  frontendPath
                  headline
                  uuid
                  video {
                    createdAt
                    duration
                    tagsPlainText
                    uuid
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },
  getVideoSnippetByUuid: (
    args?: QueryArgs<VideoSnippetByUuidQueryVariables, VideoSnippetByUuidQuery>,
  ): UseQueryOptions<VideoSnippetByUuidQuery> => {
    return {
      ...args?.options,
      queryKey: ['Sitemap.GetVideoSnippetByUuid', args?.variables],
      queryFn: async () =>
        await graphs.videosPricesFetch(
          gql`
            query VideoSnippetByUuid($uuid: String!) {
              VideoGetSnippet(uuid: $uuid) {
                description
              }
            }
          `,
          args?.variables,
        ),
    }
  },
}
