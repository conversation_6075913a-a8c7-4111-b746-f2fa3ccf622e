import SkeletonTable from '~/src/components/SkeletonTable/SkeletonTable'
import { renderFn } from '~/src/utils/SangHai/priceConversion'
import cs from '~/src/utils/cs'
import dates from '~/src/utils/dates'

export const LondonPriceByYearValues = ({
  timestamp,
  goldAM,
  goldPM,
  silver,
  platinumAM,
  platinumPM,
  palladiumAM,
  palladiumPM,
  idx,
  isLoading,
  weight,
}) => (
  <div
    className={cs([
      'grid grid-cols-5 border-b border-gray-200 text-center border-[rgba(0,0,0,0)] py-4',
      !isLoading ? 'undefined' : 'opacity-50',
      !(idx % 2) && 'bg-alt-row',
    ])}
    key={idx}
  >
    <div>
      {timestamp ? (
        dates.fmtUnix(timestamp - 1, 'MMMM DD, YYYY')
      ) : (
        <SkeletonTable />
      )}
    </div>
    <div>
      {(goldAM ?? Number.NaN) !== goldAM ? (
        <SkeletonTable />
      ) : (
        `${renderFn(weight, goldAM)} / `
      )}
      {(goldPM ?? Number.NaN) !== goldPM ? '' : renderFn(weight, goldPM)}
    </div>
    <div>
      {(silver ?? Number.NaN) !== silver ? (
        <SkeletonTable />
      ) : (
        renderFn(weight, silver)
      )}
    </div>
    <div>
      {(platinumAM ?? Number.NaN) !== platinumAM ? (
        <SkeletonTable />
      ) : (
        `${renderFn(weight, platinumAM)} / `
      )}
      {(platinumPM ?? Number.NaN) !== platinumPM
        ? ''
        : renderFn(weight, platinumPM)}
    </div>
    <div>
      {(palladiumAM ?? Number.NaN) !== palladiumAM ? (
        <SkeletonTable />
      ) : (
        `${renderFn(weight, palladiumAM)} / `
      )}
      {(palladiumPM ?? Number.NaN) !== palladiumPM
        ? ''
        : renderFn(weight, palladiumPM)}
    </div>
  </div>
)

export default LondonPriceByYearValues
