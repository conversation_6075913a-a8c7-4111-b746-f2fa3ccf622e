import { type FC, useState } from 'react'
import FutureForexTable from '~/src/components/FutureForexTable/FutureForexTable'
import type { BarchartQuote, BarchartsQuotesQuery } from '~/src/generated'
import { markets } from '~/src/lib/markets-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import * as timestamps from '~/src/utils/timestamps'

export const defaultForex = '^USDAUD,^USDCAD,^USDCNY,^USDEUR,^USDGBP,^USDJPY'

interface BarchartQuoteExtended extends BarchartQuote {
  category: string
  exchange: string
}

function dataManufacturer(res: BarchartsQuotesQuery): BarchartQuoteExtended[] {
  let hash = {}

  // @ts-ignore
  const array = []

  const results = res?.GetBarchartQuotes?.results ?? []
  results.length &&
    results.map((x) => {
      hash = { ...x, category: 'Currencies', exchange: 'CME' }
      array.push(hash)
    })

  // @ts-ignore
  return array
}

const FutureForexCell: FC<{ symbols?: string; title: 'futures' | 'forex' }> = ({
  symbols,
  title,
}) => {
  const [results, setResults] = useState<BarchartQuoteExtended[] | []>([])

  kitcoQuery(
    markets.barchartsQuotes({
      variables: {
        timestamp: timestamps.current(),
        symbols: symbols ?? defaultForex,
      },
      options: {
        // @ts-ignore
        onSuccess: (res) => setResults(() => dataManufacturer(res)),
      },
    }),
  )

  return (
    <FutureForexTable
      title={title !== 'forex' ? 'Futures' : 'Forex'}
      data={results}
      showMore={false}
    />
  )
}

export default FutureForexCell
