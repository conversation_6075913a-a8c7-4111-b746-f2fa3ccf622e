import clsx from 'clsx'
import dayjs from 'dayjs'
import type { FC } from 'react'
import { useState } from 'react'
import { BsPlusSquareFill } from 'react-icons/bs'
import { CurrencySelectCNY } from '~/src/components/CurrencySelect'
import SkeletonTable from '~/src/components/SkeletonTable/SkeletonTable'
import Table from '~/src/components/Table/Table'
import { TimeSelect } from '~/src/components/year-select/year-select.component'
import type { ShanghaiFixQuery } from '~/src/generated'
import cs from '~/src/utils/cs'
import styles from './ShanghaiLatestPrice.module.scss'

interface Props {
  data: ShanghaiFixQuery
  isLoading: boolean
}

const ColumnTitles = () => (
  <div className="grid grid-cols-3 border-b border-gray-200 px-2 py-1">
    <h4>DATE</h4>
    <h4>AM</h4>
    <h4>PM</h4>
  </div>
)

const Values = ({ timestamp, am, pm, idx, isLoading }) => (
  <div
    className={cs([
      `grid grid-cols-3 items-center border-b border-gray-200 px-2 py-1 ${
        !(idx % 2) && 'bg-alt-row'
      }`,
      !isLoading ? 'undefined' : 'opacity-50',
    ])}
  >
    <h4 className="text-gray-600 sm:text-sm lg:text-lg">
      {timestamp ? (
        dayjs.unix(timestamp).format('MMM DD, YYYY')
      ) : (
        <SkeletonTable />
      )}
    </h4>
    <h3 className="text-gray-800 sm:text-sm lg:text-lg">
      {am?.toFixed(2) || <SkeletonTable />}
    </h3>
    <h3 className="text-gray-800 sm:text-sm lg:text-lg">
      {pm?.toFixed(2) || <SkeletonTable />}
    </h3>
  </div>
)

const ShanghaiLatestPrice: FC<Props> = ({ data, isLoading }) => {
  const results = data?.GetShanghaiFixV3?.results
  const [records, setRecords] = useState(10)
  const getMoreRecord = () => {
    if (data.GetShanghaiFixV3?.results) {
      const totalRecords = data.GetShanghaiFixV3.results.length
      if (records + 10 < totalRecords) {
        setRecords(records + 10)
      } else {
        setRecords(totalRecords)
      }
    }
  }
  return (
    <div>
      <div className="mb-2 flex items-center justify-end">
        <CurrencySelectCNY classNamesListbox={styles.listbox} />
        <div className={cs(['pl-4'])}>
          <TimeSelect styleSelect={styles.selectStyle} />
        </div>
      </div>
      <Table title="Shanghai Fix Latest Price">
        <ColumnTitles />
        <Values
          timestamp={results?.[0]?.timestamp}
          am={results?.[0]?.am}
          pm={results?.[0]?.pm}
          idx={0}
          isLoading={isLoading}
        />
      </Table>
      <div className="mt-10" />

      <Table title="Shanghai Fix Historical Prices">
        <ColumnTitles />
        {results.length > 0 ? (
          <>
            {data?.GetShanghaiFixV3?.results
              .slice(1, records)
              .map(({ timestamp, am, pm }, idx: number) => (
                <Values
                  timestamp={timestamp}
                  am={am}
                  pm={pm}
                  key={idx}
                  idx={idx}
                  isLoading={isLoading}
                />
              ))}
            <ArticleMoreButton
              title={'More from Kitco Mining'}
              onClick={getMoreRecord}
            />
          </>
        ) : isLoading ? (
          <>
            {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((_, idx) => (
              <Values
                timestamp={undefined}
                am={undefined}
                pm={undefined}
                idx={undefined}
                key={idx}
                isLoading={isLoading}
              />
            ))}
          </>
        ) : (
          <div className="text-center">No data</div>
        )}
      </Table>
    </div>
  )
}

const ArticleMoreButton: FC<{
  title: string
  onClick: () => void
}> = ({ title, onClick }) => {
  return (
    <div
      onClick={onClick}
      className={clsx(
        'flex cursor-pointer items-center gap-2',
        'group hover:bg-[#1D61AE] active:bg-[#144985]',
      )}
    >
      <div
        className={clsx(
          'flex items-center justify-center gap-2',
          'w-full border border-[#E2E8F0] py-2',
        )}
      >
        <BsPlusSquareFill className="mt-[.10rem] text-[#1D61AE] group-hover:bg-[#1D61AE] group-hover:text-[#ffffff] group-active:bg-[#1D61AE] group-active:text-[#ffffff]" />
        <span className="font-bold text-[#1D61AE] underline group-hover:!text-[#ffffff] group-active:!text-[#ffffff]">
          {title}
        </span>
      </div>
    </div>
  )
}

export default ShanghaiLatestPrice
