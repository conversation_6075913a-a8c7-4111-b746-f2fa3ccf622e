import { clsx } from 'clsx'
import type { GetServerSideProps } from 'next'
import Link from 'next/link'
import Router from 'next/router'
import { type FC, useEffect, useState } from 'react'
import {
  NewsDigestByURL,
  NewsDigestLatest,
  NewsDigestStreetTalk,
  NewsOffTheWire,
} from '~/src/components-news/NewsDigest/NewDigest'
import DigetNewsTabs from '~/src/components/DigetNewsTabs/DigetNewsTabs'
import Layout from '~/src/components/Layout/Layout'
import HomePageChartCell from '~/src/features/home-page/HomePageChartCell'
import { digest } from '~/src/lib/digest-factory.lib'
import { news } from '~/src/lib/news-factory.lib'
import styles from '~/src/styles/pages/DigestNews.module.scss'
import { ssrQueries } from '~/src/utils/ssr-wrappers'

export const getServerSideProps: GetServerSideProps = async (c) => {
  const params = {
    limit: 40,
    offset: 0,
  }

  const { dehydratedState } = await ssrQueries({
    ctxRes: c.res,
    queries: [
      news.newsByCategoryGeneric({
        variables: { ...params, urlAlias: '/news/category/commodities' },
      }),
      news.newsByCategoryGeneric({
        variables: { ...params, urlAlias: '/news/category/cryptocurrencies' },
      }),
      news.newsByCategoryGeneric({
        variables: { ...params, urlAlias: '/news/category/mining' },
      }),
      news.newsOTWList({ variables: { ...params } }),
      digest.streetTalk({ variables: { ...params } }),
      digest.latestNews({ variables: { ...params } }),
    ],
  })
  return {
    props: { dehydratedState },
  }
}

const NewDigest: FC = () => {
  return (
    <Layout title="News Digest">
      <NewsDigestHead />
      <div className={clsx('gap-8', styles.digestWrapper)}>
        <div className={styles.contentRight}>
          <DigetNewsTabs />
          <ContentSection />
        </div>
        <div className={styles.chartLeft}>
          <div className={styles.chartWrapper}>
            <HomePageChartCell />
          </div>
          {/*<AdvertisingSlot*/}
          {/*  id={`adv-digest`}*/}
          {/*  className={"md:h-[600px] md:w-[300px] bg-red-400 mx-auto"}*/}
          {/*  viewportsEnabled={{*/}
          {/*    mobile: true,*/}
          {/*    tablet: false,*/}
          {/*    desktop: false,*/}
          {/*  }}*/}
          {/*/>*/}
        </div>
      </div>
    </Layout>
  )
}

export default NewDigest

const NewsDigestHead = () => {
  return (
    <div className="layoutNewsDigest mx-5 mb-5 border-b border-[#BFBFBF] xl:mx-0">
      <div
        className={clsx(
          styles.title,
          'flex items-center text-base text-[#0C87D2] md:hidden',
        )}
      >
        <h1
          className={clsx(
            'font-["Bebas_Neue"] text-[32px] uppercase leading-[38px] text-[#232323] md:text-[48px] md:leading-[58px]',
          )}
        >
          Market News Digest
        </h1>
      </div>
      <div className="hidden items-center justify-between md:flex">
        <div className="mb-1 flex items-center leading-[38px] md:leading-[58px]">
          <Link href={'/news'}>
            <h1 className="font-['Bebas_Neue'] text-[32px] uppercase text-ktc-date-gray md:text-[48px]">
              NEWS
            </h1>
          </Link>
          <h1 className="px-1 font-['Bebas_Neue'] text-[32px] uppercase text-ktc-date-gray md:px-2 md:text-[48px]">
            /
          </h1>
          <h1 className="font-['Bebas_Neue'] text-[32px] uppercase leading-[38px] text-[#373737] md:text-[48px] md:leading-[58px]">
            Market News Digest
          </h1>
        </div>
      </div>
    </div>
  )
}

const ContentSection = () => {
  const { router } = Router
  const [currentUrl, setCurrentUrl] = useState('')
  useEffect(() => {
    if (!window) return
    setCurrentUrl(window.location.hash)
  }, [router?.asPath])

  switch (currentUrl) {
    case '#metals':
      return (
        <NewsDigestByURL
          urlAlias="/news/category/commodities"
          title="Metals News"
        />
      )

    case '#crypto':
      return (
        <NewsDigestByURL
          urlAlias="/news/category/cryptocurrencies"
          title="Crypto News"
        />
      )

    case '#mining':
      return (
        <NewsDigestByURL urlAlias="/news/category/mining" title="Mining News" />
      )

    case '#market':
      return <NewsOffTheWire title="Market News" />

    case '#streettalk':
      return <NewsDigestStreetTalk />

    default:
      return <NewsDigestLatest />
  }
}
