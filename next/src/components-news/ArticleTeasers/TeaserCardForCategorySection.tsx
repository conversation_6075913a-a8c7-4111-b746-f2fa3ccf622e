import Link from 'next/link'
import type { FC } from 'react'
import { CategoryLink } from '~/src/components-news/ArticleTeasers/CategoryLink'
import { DateStamp } from '~/src/components-news/ArticleTeasers/DateStamp'
import type { TeaserCardProps } from '~/src/components-news/ArticleTeasers/TeaserCard'
import { ImageMS } from '~/src/components/ImageMS/ImageMS.component'
import { Spacer } from '~/src/components/spacer/spacer.component'
import cs from '~/src/utils/cs'

export const TeaserCardForCategorySection: FC<TeaserCardProps> = ({
  node,
  size,
  hideCategory,
  hideSummary,
  classWrapper,
}: TeaserCardProps) => {
  const sizeCSS = {
    sm: '',
    md: 'leading-[130%] line-clamp-2',
    lg: '',
    xl: '',
  }

  return (
    <div className={cs(['flex w-full', classWrapper])}>
      <Link
        className="relative block aspect-video"
        href={node?.urlAlias ?? '/'}
      >
        <ImageMS
          src={
            node?.teaserImage?.detail?.default?.srcset ??
            node?.image?.detail?.default?.srcset ??
            node?.legacyThumbnailImageUrl
          }
          hasLegacyThumbnailImageUrl={!!node?.legacyThumbnailImageUrl}
          alt={`${node?.title} teaser image`}
          priority={true}
          width={304}
          height={170}
          service="icms"
          className="aspect-video rounded-lg object-cover"
        />
      </Link>
      {!hideCategory ? (
        <>
          <CategoryLink
            urlAlias={node?.category?.urlAlias}
            text={node?.category?.name}
          />
          <div className="h-2 bg-transparent" />
        </>
      ) : (
        <Spacer className="h-3" />
      )}
      <Link href={node?.urlAlias ?? '/'}>
        <>
          <h3
            className={cs([
              'text-[20px] md:min-h-[44px] md:text-[17px]',
              sizeCSS[size],
            ])}
          >
            {node?.teaserHeadline ?? node?.title}
          </h3>
          {hideSummary ? null : (
            <div className="pt-2">
              <div
                className="summary md:min-h-[60px]"
                dangerouslySetInnerHTML={{ __html: node?.teaserSnippet }}
              />
            </div>
          )}
          <Spacer className="h-2" />
          <DateStamp stamp={node?.createdAt} classNames="" />
        </>
      </Link>
    </div>
  )
}
