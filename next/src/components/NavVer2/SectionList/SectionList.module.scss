.iconTitleContainer {
  display: flex;
  width: inherit;
  margin-right: 20px;
  white-space: nowrap;

  @media screen and (max-width: 767px) {
    white-space: unset;
  }

  ul {
    margin-bottom: 0.75em;

    & li {
      padding: 0.1em 0;
    }
  }

  &:last-child ul {
    margin-bottom: 0;
  }
}

.iconContainer {
  display: block;
  margin-right: 0.5em;
  margin-top: 5px;

  @media screen and (max-width: 768px) {
    display: none;
  }
}
.title {
  margin-bottom: 0.2em;
}

.item {
  color: #ededed;

  &:hover {
    text-decoration: underline;
    color: #fff;
  }
}
