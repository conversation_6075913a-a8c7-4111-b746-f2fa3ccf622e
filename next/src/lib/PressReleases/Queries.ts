import type { UseQueryOptions } from '@tanstack/react-query'
// @ts-ignore
import { gql } from 'graphql-request'
import type {
  NewsGenericPressReleasesQuery,
  NewsGenericPressReleasesQueryVariables,
  NodeListQueueQuery,
  NodeListQueueQueryVariables,
} from '~/src/generated'
import { graphs } from '~/src/services/database/fetcher'
import type QueryArgs from '~/src/types/QueryArgs'

export const pressReleases = {
  nodeListPressReleases: (
    args: QueryArgs<
      NewsGenericPressReleasesQueryVariables,
      NewsGenericPressReleasesQuery
    >,
  ): UseQueryOptions<NewsGenericPressReleasesQuery> => {
    // @ts-ignore
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['nodeListPressReleases', args?.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query NewsGenericPressReleasesUhm($limit: Int, $offset: Int) {
              nodeList(
                limit: $limit
                offset: $offset
                bundles: [PressRelease]
              ) {
                items {
                  ... on PressRelease {
                    id
                    title
                    teaserHeadline
                    createdAt
                    updatedAt
                    url
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  nodeListPressReleasesQueue: (
    args: QueryArgs<NodeListQueueQueryVariables, NodeListQueueQuery>,
  ): UseQueryOptions<NodeListQueueQuery> => {
    // @ts-ignore
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['nodeListPressReleases', args?.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query NewsGenericPressReleases(
              $limit: Int
              $offset: Int
              $queueId: String = "press_releases"
            ) {
              queue: nodeListQueue(
                limit: $limit
                offset: $offset
                queueId: $queueId
                bundles: [PressRelease]
              ) {
                items {
                  ... on PressRelease {
                    id
                    title
                    teaserHeadline
                    createdAt
                    updatedAt
                    url
                  }
                }
              }

              ids: nodeIdsInQueue(queueId: $queueId)
            }
          `,
          args?.variables,
        ),
    }
  },
}
