import type { <PERSON> } from 'react'
import { TagLink } from '~/src/components/tag-link/tag-link.component'
import type { Tag } from '~/src/generated'

export const Tags: FC<{ data: Tag[] }> = ({ data }) => {
  return (
    <div className="rounded-xl border border-ktc-borders p-5">
      <h3 className="font-mulish pb-2 leading-5">
        <span>Tags:</span>
      </h3>
      <div className="flex flex-wrap gap-2">
        {data?.map((t) => (
          <TagLink key={t.id} href={t.urlAlias} name={t.name} />
        ))}
      </div>
    </div>
  )
}
