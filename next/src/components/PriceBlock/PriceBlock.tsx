import clsx from 'clsx'
import LazyCurrencySelect from '~/src/components/PriceBlock/LazyCurrencySelect'
import type { CryptosTableQuery } from '~/src/generated'
import { useCurrency } from '~/src/hooks/Currency/useCurrency'
import { cryptos } from '~/src/lib/cryptos-factory.lib'
import styles from '~/src/pages/price/crypto/symbol.module.scss'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import { currencyFmt } from '~/src/utils/currencyFmt'

/**
 * PriceBlock component
 * This component is used to display the price of a cryptocurrency
 *
 * @param symbol
 * @constructor
 */
const PriceBlock = ({ symbol }) => {
  const currency = useCurrency()

  const { data } = kitcoQuery<CryptosTableQuery>(
    cryptos.cryptosTable({
      variables: {
        symbols: symbol,
        currency: currency?.symbol ?? 'USD',
      },
      options: {
        refetchInterval: 60 * 1000, // every minute
      },
    }),
  )

  const dataCrypto: any =
    data?.GetCryptoComparePriceFullV3 &&
    data?.GetCryptoComparePriceFullV3.length > 0
      ? data?.GetCryptoComparePriceFullV3[0]
      : null

  const change = dataCrypto?.change24Hour ?? 0

  const isUp = () => {
    if (!change) return

    const test = change.toString()

    return test.charAt(0) !== '-'
  }

  const styleUpOrDown = !isUp() ? clsx(styles.down) : clsx(styles.up)

  return (
    <div className="relative mb-[30px] rounded-lg border border-[#E5E5E5] px-[15px] pb-[17px] pt-[10px] leading-5">
      <div className="border-b border-ktc-borders">
        <div className="mb-px ml-0.5 text-[13px] font-normal">Price</div>
        <div className="mb-2 text-right">
          <h3 className="font-mulish mb-[3px] text-4xl font-bold leading-normal tracking-[1px]">
            {currencyFmt(dataCrypto?.price) || '-'}
          </h3>
          <div className="absolute right-[15px] top-[-15px] bg-white">
            <LazyCurrencySelect classNamesListbox={styles.listbox} />
          </div>

          <div className={clsx(styles.currencyChangeDate, 'mb-4 mr-0.5')}>
            <span className={clsx(styleUpOrDown, 'text-[15px]')}>
              {isUp() ? '+' : ''}
              {currencyFmt(dataCrypto?.change24Hour) ?? '-'}
              &nbsp;
            </span>
            <span className={clsx(styleUpOrDown, 'text-[15px]')}>
              ({isUp() ? '+' : ''}
              {currencyFmt(dataCrypto?.changePct24HourCalculated) ?? '-'}
              %)
            </span>
          </div>
        </div>
      </div>
      <div className="mt-2">
        <div className="mb-1 flex items-center justify-between">
          <div className="text-sm font-normal capitalize">Market Cap</div>
          <span className="mr-0.5 block text-[14px] font-normal">
            {currencyFmt(dataCrypto?.mktCap) || '-'}
          </span>
        </div>
        <div className="mb-10 flex items-center justify-between">
          <div className="text-sm font-normal capitalize">Volume</div>
          <span className="mr-0.5 block text-[14px] font-normal">
            {currencyFmt(dataCrypto?.totalVolume24hTo) || '-'}
          </span>
        </div>
        {dataCrypto?.highDay && (
          <>
            <div className={styles.priceToday}>
              <div>{currencyFmt(dataCrypto?.lowDay)}</div>
              <div>{currencyFmt(dataCrypto?.highDay)}</div>
            </div>
            <div className="pt-2 text-center text-[14px] font-normal leading-[18px]">
              Day&apos;s Range
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default PriceBlock
