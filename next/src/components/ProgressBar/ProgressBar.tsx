import clsx from 'clsx'
import { type FC, useEffect, useRef, useState } from 'react'

/**
 * Progress Bar Props
 */
interface ProgressBarProps {
  intervalTime: number
  index: number
  paused?: boolean
  resetOnPause?: boolean
  startFrom?: number
}

/**
 * Progress Bar Component
 * This component is used to display a progress bar that animates from left to right
 *
 * @param intervalTime
 * @param index
 * @param paused
 * @param resetOnPause
 * @param startFrom
 * @constructor
 */
const ProgressBar: FC<ProgressBarProps> = ({
  intervalTime = 5000,
  index,
  paused = false,
  resetOnPause = false,
  startFrom = 0,
}) => {
  // Create a reference for the progress bar
  const barRef = useRef<HTMLDivElement>(null)

  // Create a state for the animation key
  const [animationKey, setAnimationKey] = useState(0)

  // Define the keyframes for the progress animation
  const progressAnimation = `
    @keyframes progress-${animationKey} {
      from { 
        transform: scaleX(${startFrom}); 
        transform-origin: left; 
      }
      to { 
        transform: scaleX(1); 
        transform-origin: left; 
      }
    }
  `

  /**
   * Reset the animation when paused
   */
  useEffect(() => {
    if (paused && resetOnPause) {
      setAnimationKey((prevKey) => prevKey + 1)
    }
  }, [paused, resetOnPause])

  /**
   * Restart the animation when the index or startFrom changes
   */
  useEffect(() => {
    setAnimationKey((prevKey) => prevKey + 1)
  }, [index, startFrom])

  return (
    <div className="relative h-0.5 w-full overflow-hidden bg-gray-100">
      <style>{progressAnimation}</style>
      <div
        ref={barRef}
        key={animationKey}
        className={clsx('h-full bg-gray-300')}
        style={{
          animationName: `progress-${animationKey}`,
          animationDuration: `${intervalTime}ms`,
          animationTimingFunction: 'linear',
          animationFillMode: 'forwards',
          animationPlayState: paused ? 'paused' : 'running',
        }}
      />
    </div>
  )
}

export default ProgressBar
