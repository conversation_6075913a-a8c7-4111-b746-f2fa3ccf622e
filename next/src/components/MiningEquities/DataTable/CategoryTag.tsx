import clsx from 'clsx'
import type { FC } from 'react'

/**
 * Get the style for the category tag based on the category type.
 *
 * @param {string} type - The category type.
 * @param {boolean} text - Whether to return the text style or the border style.
 * @returns {string} The style for the category tag.
 */
function tagStyle(type: string, text = true) {
  switch (type) {
    case 'Gold':
      return text ? 'text-[#ECB30E]' : 'border-[#ECB30E]'
    case 'Silver':
      return text ? 'text-[#848484]' : 'border-[#848484]'
    case 'Base Metals':
      return text ? 'text-[#5E5E5E]' : 'border-[#5E5E5E]'
    case 'Energy':
      return text ? 'text-[#1D61AE]' : 'border-[#1D61AE]'
    case 'PGM':
      return text ? 'text-[#C1BA9E]' : 'border-[#C1BA9E]'
    case 'Battery Materials':
      return text ? 'text-[#4B4E5B]' : 'border-[#4B4E5B]'
    case 'Rare Earth':
      return text ? 'text-[#E3623A]' : 'border-[#E3623A]'
    case 'Specialty Metals':
      return text ? 'text-[#908D6C]' : 'border-[#908D6C]'
    default:
      return text ? 'text-grey-400' : 'border-grey-400'
  }
}

/**
 * CategoryTag component props.
 *
 * @param {string | Array<string>} value - The category value.
 */
interface CategoryTagProps {
  value: string | Array<string>
}

/**
 * Renders a category tag component.
 *
 * @param {CategoryTagProps} props - The component props.
 * @returns {JSX.Element} The rendered category tag component.
 */
const CategoryTag: FC<CategoryTagProps> = ({
  value,
}: CategoryTagProps): JSX.Element => {
  // Make sure the value is an array always
  const values = Array.isArray(value) ? value : [value]

  return (
    <div className="flex flex-wrap gap-1">
      {values.map((item) => (
        <div
          key={item}
          className={clsx(
            'flex items-center justify-center gap-2.5 rounded border px-1.5 py-0.5',
            tagStyle(item, false),
          )}
          style={{
            willChange: 'transform',
          }}
        >
          <div
            className={clsx(
              "whitespace-nowrap font-['Lato'] text-xs font-bold leading-3 tracking-tight",
              tagStyle(item),
            )}
            style={{
              willChange: 'transform',
            }}
          >
            {item}
          </div>
        </div>
      ))}
    </div>
  )
}

export default CategoryTag
