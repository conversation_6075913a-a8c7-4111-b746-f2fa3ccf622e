/* src/components-metals/LondonFixGrid/LondonFixGrid.module.scss */

/* 0. Header bar - Ribbon style */
.header {
  @apply relative flex items-center justify-between text-white;
  height: 37px;
  padding: 15px 30px; /* Increased padding to keep icons within ribbon */
  background: transparent;

  /* Ribbon background - narrower than full width */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 20px;
    right: 20px;
    bottom: 0;
    background-color: #383838;
    z-index: -1;
  }
}

.headerTitle {
  font-family: 'Lato', sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #ffffff;
  text-align: center;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: auto;
  white-space: nowrap;
}

.headerIcon {
  width: 21px;
  height: 21px;
  color: #ffffff;
}

.widgetIcon {
  width: 23px;
  height: 23px;
  color: #ffffff;
}

/* 1. Wrapper: centers content, vertical margin, max-width ≈900px with shadow */
.wrapper {
  @apply mx-auto my-8 max-w-4xl rounded-md;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.12);
  padding-top: 1rem; /* Add gap at the top of wrapper for desktop */
}

/* 2. Controls row */
.controls {
  @apply grid grid-cols-3 items-center px-6;
  padding-top: 12px;
  padding-bottom: 20px;
  gap: 15px;
}

.datePicker {
  width: 200px;
  height: 30px;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 0 8px;
  font-size: 14px;
}

.infoLabel {
  font-family: 'Mulish', sans-serif;
  font-weight: 300;
  font-size: 11px;
  line-height: 14px;
  color: #000000;
  text-align: center;

  strong {
    font-weight: 600;
  }
}

.controlsRight {
  @apply flex justify-end space-x-4;
}

.currencySelect {
  width: 125px;
  height: 30px;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 0 8px;
  font-size: 14px;
}

.uomSelect {
  width: 60px;
  height: 30px;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 0 8px;
  font-size: 14px;
}

/* 3. Grid container: 2×2 with responsive layout */
.gridContainer {
  @apply grid grid-cols-2 grid-rows-2 px-6 pb-6;
  gap: 0;
  /* Ensure cards fit together perfectly */
  width: fit-content;
  margin: 0 auto;
}

/* 4. Card ("tile"): "+" style separators with diagonal approach */
.gridify {
  @apply flex flex-col bg-white;
  width: 335px;
  min-height: 65px;
  padding: 12px;
  border: none; /* Reset all borders */
  margin: 0; /* Ensure no margins */
  box-sizing: border-box; /* Include padding and borders in width calculation */

  /* HK (1st card - top-left): right + bottom borders */
  &:nth-child(1) {
    border-right: 1px solid #e5e7eb;
    border-bottom: 1px solid #e5e7eb;
  }

  /* Mumbai (2nd card - top-right): bottom border only */
  &:nth-child(2) {
    border-bottom: 1px solid #e5e7eb;
  }

  /* London (3rd card - bottom-left): right border only */
  &:nth-child(3) {
    border-right: 1px solid #e5e7eb;
  }

  /* NY (4th card - bottom-right): no borders needed */
  &:nth-child(4) {
    /* No borders - clean corner */
  }
}
/* Card Header */
.cardHeader {
  background: #fafafa;
  width: 100%;
  height: 40px;
  padding: 0 12px;
  @apply flex items-center justify-between;
  margin-bottom: 4px; /* Very minor gap between card header and data */
}

.flagIcon {
  width: 32px;
  height: 22px;
  padding-left: 10px;
  @apply flex items-center;
}

.cityTimeContainer {
  @apply text-right;
}

.cityLabel {
  font-family: 'Lato', sans-serif;
  font-weight: 600;
  font-size: 14.67px;
  line-height: 16px;
  color: #000000;
  text-transform: uppercase;
}

.timestamp {
  font-family: 'Lato', sans-serif;
  font-weight: 300;
  font-size: 12px;
  line-height: 16px;
  color: #000000;
}

/* Data Table */
.dataTable {
  margin-top: 4px; /* Very minor gap between card header and data */
  border-collapse: collapse;
  width: 100%;
}

.dataTable td,
.dataTable th {
  border: none; /* No border lines for metal data */
  padding: 6px 0; /* Vertical padding for table cells */
}

.labelCell {
  font-family: 'Lato', sans-serif;
  font-weight: 600;
  font-size: 12px;
  line-height: 16px;
  color: #000000;
  text-align: left;
}

.priceCell {
  font-family: 'Lato', sans-serif;
  font-weight: 300;
  font-size: 13.33px;
  line-height: 16px;
  color: #000000;
  text-align: right;
}

.priceCell.pending {
  color: #4f81bd;
}

.countdown {
  font-family: 'Lato', sans-serif;
  font-weight: 300;
  font-size: 12px;
  line-height: 16px;
  color: #4f81bd;
}

.blueText {
  color: #4f81bd;
}

.disabledOverlay {
  background-color: #f1f5fa;
  border-radius: 8px; /* Rounded borders for pending/coming overlay */
  padding: 0.5rem;
}
