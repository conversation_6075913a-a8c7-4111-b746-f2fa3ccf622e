import getMarketSchedule from './getMarketSchedule'

describe('getMarketSchedule', () => {
  afterEach(() => {
    // Reset environment variables after each test
    delete process.env.NEXT_PUBLIC_MARKETS_OPEN_HOUR
    delete process.env.NEXT_PUBLIC_MARKETS_CLOSE_HOUR
    delete process.env.NEXT_PUBLIC_MARKETS_OPEN_DAY
    delete process.env.NEXT_PUBLIC_MARKETS_CLOSE_DAY
  })

  /**
   * Test case when no environment variables are set.
   * The function should return the default values.
   */
  it('should return default values when no environment variables are set', () => {
    const result = getMarketSchedule()

    expect(result).toEqual({
      closeDay: 5, // Friday
      closeHour: 17, // 5 PM
      openDay: 0, // Sunday
      openDays: [0, 1, 2, 3, 4], // Sunday to Thursday (open until Friday)
      openHour: 18, // 6 PM
    })
  })

  /**
   * Test case when all environment variables are set.
   * The function should return the values from the environment variables.
   */
  it('should return the correct values from environment variables', () => {
    process.env.NEXT_PUBLIC_MARKETS_OPEN_HOUR = '9' // 9 AM
    process.env.NEXT_PUBLIC_MARKETS_CLOSE_HOUR = '16' // 4 PM
    process.env.NEXT_PUBLIC_MARKETS_OPEN_DAY = '1' // Monday
    process.env.NEXT_PUBLIC_MARKETS_CLOSE_DAY = '4' // Thursday

    const result = getMarketSchedule()

    expect(result).toEqual({
      closeDay: 4, // Thursday
      closeHour: 16, // 4 PM
      openDay: 1, // Monday
      openDays: [1, 2, 3], // Monday to Wednesday (open until Thursday)
      openHour: 9, // 9 AM
    })
  })

  /**
   * Test case when environment variables have invalid values.
   * The function should fall back to the default values.
   */
  it('should fall back to default values when environment variables are invalid', () => {
    process.env.NEXT_PUBLIC_MARKETS_OPEN_HOUR = 'invalid'
    process.env.NEXT_PUBLIC_MARKETS_CLOSE_HOUR = 'invalid'
    process.env.NEXT_PUBLIC_MARKETS_OPEN_DAY = 'invalid'
    process.env.NEXT_PUBLIC_MARKETS_CLOSE_DAY = 'invalid'

    const result = getMarketSchedule()

    expect(result).toEqual({
      closeDay: 5, // Friday
      closeHour: 17, // 5 PM
      openDay: 0, // Sunday
      openDays: [0, 1, 2, 3, 4], // Sunday to Thursday
      openHour: 18, // 6 PM
    })
  })

  /**
   * Test case when only some environment variables are set.
   * The function should return a mix of default and environment-provided values.
   */
  it('should return a mix of default and environment-provided values', () => {
    process.env.NEXT_PUBLIC_MARKETS_OPEN_HOUR = '7' // 7 AM
    process.env.NEXT_PUBLIC_MARKETS_CLOSE_DAY = '3' // Wednesday

    const result = getMarketSchedule()

    expect(result).toEqual({
      closeDay: 3, // Wednesday
      closeHour: 17, // Default 5 PM
      openDay: 0, // Default Sunday
      openDays: [0, 1, 2], // Sunday to Tuesday (open until Wednesday)
      openHour: 7, // 7 AM (from environment variable)
    })
  })
})
