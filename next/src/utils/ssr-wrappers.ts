import { type QueryClient, dehydrate } from '@tanstack/react-query'
import type { NextPageContext } from 'next'
import { kitcoQueryClient } from '~/src/services/database/kitcoQuery'

/**
 * Interface for the arguments passed to the ssrQueries function
 * @interface SSRArgs
 * @property {Array<never | any>} queries - Array of queries to be prefetched
 * @property {NextPageContext['res']} [ctxRes] - Optional response object from Next.js context
 * @property {QueryClient} [queryClient] - Optional QueryClient instance
 */
interface SSRArgs {
  queries: Array<never | any>
  ctxRes?: NextPageContext['res']
  queryClient?: QueryClient
}

/**
 * Function to create a QueryClient if not provided
 * @param {QueryClient} [queryClient] - Optional QueryClient instance
 * @returns {QueryClient} - A QueryClient instance
 */
function getQueryClient(queryClient?: QueryClient): QueryClient {
  return queryClient || kitcoQueryClient()
}

/**
 * Function to prefetch queries
 * @param {QueryClient} queryClient - The QueryClient instance
 * @param {Array<any>} queries - Array of queries to be prefetched
 * @returns {Promise<void>} - A promise that resolves when all queries are prefetched
 */
async function prefetchQueries(
  queryClient: QueryClient,
  queries: Array<any>,
): Promise<void> {
  await Promise.allSettled(
    queries.map((query) =>
      queryClient.prefetchQuery({
        queryKey: query.queryKey,
        queryFn: query.queryFn,
      }),
    ),
  )
}

/**
 * Function to extract and dedupe cache tags
 * @param {QueryClient} queryClient - The QueryClient instance
 * @returns {string} - A string of deduped cache tags
 */
function getCacheTags(queryClient: QueryClient): string {
  const cacheTagsHeader = queryClient
    .getQueryCache()
    .getAll()
    // @ts-ignore
    .map((query) => query.state.data?.cacheTagsHeader)
    .filter(Boolean)
    .join(' ')

  return [...new Set(cacheTagsHeader.split(' '))].join(' ')
}

/**
 * Function to set response headers
 * @param {NextPageContext['res']} ctxRes - The response object from Next.js context
 * @param {string} cacheTags - The cache tags to be set in the headers
 */
function setResponseHeaders(ctxRes: NextPageContext['res'], cacheTags: string) {
  ctxRes?.setHeader('Surrogate-Key', cacheTags)
  ctxRes?.setHeader(
    'Cache-Control',
    'max-age=60, s-maxage=60, stale-while-revalidate=60',
  )
}

/**
 * Main function to handle server-side rendering of queries
 *
 * @param {SSRArgs} args - The arguments for the function
 * @returns {Promise<{ dehydratedState: any }>} - The dehydrated state of the queries
 */
export async function ssrQueries({
  queries,
  ctxRes,
  queryClient,
}: SSRArgs): Promise<{ dehydratedState: any }> {
  // Create a new QueryClient if not provided
  const client = getQueryClient(queryClient)

  // Try to Prefetch queries
  try {
    await prefetchQueries(client, queries)
  } catch (err) {
    console.error('error in utils/ssrQueries\n', err)
  }

  // Dehydrate the client and get cache tags
  const dehydratedState = JSON.parse(JSON.stringify(dehydrate(client)))
  const cacheTags = getCacheTags(client)

  // Set response headers if provided
  if (ctxRes) {
    setResponseHeaders(ctxRes, cacheTags)
  }

  return {
    dehydratedState,
  }
}
