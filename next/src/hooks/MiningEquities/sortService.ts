import type { SortingState } from '@tanstack/table-core'
import type MiningEquity from '~/src/types/DataTable/MiningEquity'

/**
 * Sort the data if column is not available
 *
 * @param data
 * @param sortBy
 */
export const sortData = (
  data: MiningEquity[],
  sortBy: SortingState,
): MiningEquity[] => {
  // If the sorting state is empty, return the data as is
  if (!sortBy || sortBy.length <= 0) return data

  // Sort the data based on the sorting state
  const { id, desc } = sortBy[0]

  return [...data].sort((a, b) => {
    const aValue = a[id]
    const bValue = b[id]
    if (aValue < bValue) return desc ? 1 : -1
    if (aValue > bValue) return desc ? -1 : 1
    return 0
  })
}
