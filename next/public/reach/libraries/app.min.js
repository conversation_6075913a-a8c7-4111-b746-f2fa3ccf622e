/* eslint-disable */
$(function () {
  function n() {
    var n = $(window).scrollTop()
    n > 0
      ? $('.navbar').addClass('navbar--sticky')
      : $('.navbar').removeClass('navbar--sticky')
  }
  $('.copy-year').text(new Date().getFullYear()),
    $(window).scroll(function () {
      n()
    }),
    n()
  var a = $('html, body'),
    e = location.hash
  e && $(e).length > 0 && a.animate({ scrollTop: $(e).offset().top }, 500),
    $('a[href^="#"]').click(function () {
      var n = $.attr(this, 'href')
      return (
        a.animate({ scrollTop: $(n).offset().top }, 500, function () {
          window.location.hash = n
        }),
        !1
      )
    })
  var t = $('.language-switcher__current-lang'),
    o = $('.language-switcher__dropdown')
  t.text($('html').attr('lang')),
    t.click(function () {
      o.addClass('language-switcher__dropdown--open')
    }),
    $(document).click(function (n) {
      t.is(n.target) ||
        0 !== t.has(n.target).length ||
        o.removeClass('language-switcher__dropdown--open')
    }),
    $('#mobile-menu-button').click(function () {
      $('body').hasClass('menu-open')
        ? $('body').removeClass('menu-open')
        : $('body').addClass('menu-open')
    })
    // $('.js-select-input').select2({ minimumResultsForSearch: 1 / 0 })
})
//# sourceMappingURL=data:application/json;charset=utf8;base64,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
