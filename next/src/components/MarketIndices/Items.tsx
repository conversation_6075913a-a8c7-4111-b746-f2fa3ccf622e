import clsx from 'clsx'
import Link from 'next/link'
import type { FC } from 'react'
import type { BarchartQuote } from '~/src/generated'
import colorize from '~/src/utils/colorize'
import isNegative from '~/src/utils/isNegative'
import priceFormatter from '~/src/utils/priceFormatter'
import Icon from '../Icon/Icon'
import styles from './MarketIndices.module.scss'

type ItemProps = {
  v: Partial<BarchartQuote>
  itemClass: string
}

const Items: FC<ItemProps> = ({ v, itemClass }) => {
  return (
    <div className={clsx(styles.indexContainer, itemClass)}>
      <h3 className="flex items-center">
        <Icon
          icon={!isNegative(v.percentChange) ? 'arrow-up' : 'arrow-down'}
          size="12px"
          color={!isNegative(v.percentChange) ? '#18A751' : '#A70202'}
        />
        &nbsp;
        <Link
          className="text-kitco-black hover:text-ktc-blue hover:underline transition-colors duration-200 ease-in-out"
          href={`/markets/indices/${v.symbol}`}
        >
          {v.name}
        </Link>
      </h3>
      <div className={styles.currentChangeFlex}>
        <p className={colorize(v.percentChange)}>
          {priceFormatter(v.lastPrice)}
        </p>
        <p className={colorize(v.percentChange)}>{v.percentChange}%</p>
      </div>
    </div>
  )
}

export default Items
