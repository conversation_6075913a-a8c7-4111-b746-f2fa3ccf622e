import {
  Combobox,
  ComboboxButton,
  ComboboxInput,
  ComboboxOption,
  ComboboxOptions,
  Transition,
} from '@headlessui/react'
import clsx from 'clsx'
import React, { useState } from 'react'
import { <PERSON><PERSON>he<PERSON>, HiChevronDown } from 'react-icons/hi'
import DATA from './DATA.json'

export type ItemSelect = {
  name: string
  code: string
}

interface Props {
  defaultSelected: ItemSelect
  onSelectChange: (val: ItemSelect) => void
}

const SelectBox: React.FC<Props> = (Props) => {
  const { defaultSelected, onSelectChange } = Props
  const [selected, setSelected] = useState(defaultSelected)

  const onChange = (value: ItemSelect) => {
    setSelected(value)
    onSelectChange(value)
  }
  const [query, setQuery] = useState('')

  const filteredCountries =
    query === ''
      ? DATA
      : DATA.filter((person) => {
          return person.name.toLowerCase().includes(query.toLowerCase())
        })

  return (
    <>
      <Combobox value={selected} onChange={onChange}>
        <div className="relative">
          <ComboboxInput
            className={clsx(
              'w-full border border-gray-300  py-1.5 pr-8 pl-3 text-sm/6 text-zinc-400',
              'focus:outline-none data-[focus]:outline-2 data-[focus]:-outline-offset-2 data-[focus]:outline-zinc/25',
            )}
            displayValue={(DATA: any) => DATA?.name}
            onChange={(event) => setQuery(event.target.value)}
          />
          <ComboboxButton className="group absolute inset-y-0 right-0 px-2.5">
            <HiChevronDown className="size-4 fill-gray/60 group-data-[hover]:fill-gray" />
          </ComboboxButton>
        </div>
        <Transition
          leave="transition ease-in duration-100"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <ComboboxOptions
            anchor="bottom"
            className="w-[var(--input-width)] bg-slate-800 p-1 [--anchor-gap:var(--spacing-1)] empty:hidden"
          >
            {filteredCountries.map((countries) => (
              <ComboboxOption
                key={countries.name}
                value={countries}
                className="group flex cursor-default items-center gap-2 rounded-lg py-1.5 px-3 select-none data-[focus]:bg-white/10"
              >
                {countries?.code === defaultSelected?.code && (
                  <HiCheck className="size-4 fill-white " />
                )}

                <div className="text-sm/6 text-white">{countries.name}</div>
              </ComboboxOption>
            ))}
          </ComboboxOptions>
        </Transition>
      </Combobox>
    </>
  )
}

export default SelectBox
