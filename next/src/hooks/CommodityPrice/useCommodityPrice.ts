import type { MetalQuoteQuery } from '~/src/generated'
import { metals } from '~/src/lib/metals-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import type Currency from '~/src/types/Currency'
import type TPossibleRenders from '~/src/types/TPossibleRenders'
import { convert } from '~/src/utils/price-conversion'
import { pf } from '~/src/utils/priceFormatter'
import * as timestamps from '~/src/utils/timestamps'

/**
 * Props for the useCommodityPrice hook
 */
interface UseCommodityPriceProps {
  data: MetalQuoteQuery
  currency: Currency
  symbol: string
  isBaseMetal: boolean
}

/**
 * Hook for getting the commodity price
 *
 * @param data
 * @param currency
 * @param symbol
 * @param isBaseMetal
 */
export const useCommodityPrice = ({
  data,
  currency,
  symbol,
  isBaseMetal,
}: UseCommodityPriceProps) => {
  // Get annual data from the API
  const dataAnnual = getAnnualData(currency, symbol)

  // Data Results
  const res = data?.GetMetalQuoteV3?.results[0]

  // Get the change, ask, bid, and change percentage
  const change = res?.change
  const ask = res?.ask
  const bid = res?.bid
  const changePercentage = res?.changePercentage

  return {
    data,
    dataAnnual,
    res,
    change,
    ask,
    bid,
    changePercentage,
    isUp: getIsUp(change),
    displayNullOrValue: (val: TPossibleRenders) =>
      displayValue(val, { ask, bid, change, changePercentage, isBaseMetal }),
  }
}

/**
 * Get annual data from the API
 *
 * @param currency
 * @param symbol
 */
const getAnnualData = (currency: Currency, symbol: string) => {
  const { data } = kitcoQuery(
    metals.metalMonthAnnual({
      variables: {
        symbol: symbol || 'AU',
        currency: currency.symbol || 'USD',
        timestamp: timestamps.current(),
      },
      options: {},
    }),
  )

  return data
}

/**
 * Check if the change is positive
 *
 * @param change
 */
const getIsUp = (change: number | undefined): boolean => {
  if (!change) return false
  return change.toString().charAt(0) !== '-'
}

/**
 * Function for formatting and displaying values
 *
 * @param val
 * @param ask
 * @param bid
 * @param change
 * @param changePercentage
 * @param isBaseMetal
 */
const displayValue = (
  val: TPossibleRenders,
  {
    ask,
    bid,
    change,
    changePercentage,
    isBaseMetal,
  }: {
    ask?: number
    bid?: number
    change?: number
    changePercentage?: number
    isBaseMetal: boolean
  },
): string => {
  switch (val) {
    case 'ask':
      return formatAsk(ask)
    case 'askBM':
      return formatAskBM(ask)
    case 'bidBM':
      return formatBidBM(bid)
    case 'bid':
      return formatBid(bid)
    case 'change':
      return formatChange(change, isBaseMetal)
    case 'change_percentage':
      return formatChangePercentage(changePercentage)
    case 'convert_price_gram':
      return convertPriceToGram(bid)
    case 'convert_price_kilo':
      return convertPriceToKilo(bid)
    case 'convert_price_penny':
      return convertPriceToPennyweight(bid)
    case 'convert_price_tola':
      return convertPriceToTola(bid)
    case 'convert_price_tael':
      return convertPriceToTael(bid)
    case 'convert_change_kilo':
      return convertChangeToKilo(bid, change)
    case 'convert_change_gram':
      return convertChangeToGram(bid, change)
    case 'convert_change_penny':
      return convertChangeToPennyweight(bid, change)
    case 'convert_change_tola':
      return convertChangeToTola(bid, change)
    case 'convert_change_tael':
      return convertChangeToTael(bid, change)
    case 'convert_price_pound_bmt':
      return convertPriceToPoundBMT(bid)
    case 'convert_price_ton_bmt':
      return convertPriceToTonBMT(bid)
    case 'convert_price_tonne_bmt':
      return convertPriceToTonneBMT(bid)
    case 'convert_change_ton_bmt':
      return convertChangeToTonBMT(bid, change)
    case 'convert_change_tonne_bmt':
      return convertChangeToTonneBMT(bid, change)
    case 'convert_change_kilo_bmt':
      return convertChangeToKiloBMT(bid, change)
    case 'convert_change_gram_bmt':
      return convertChangeToGramBMT(bid, change)
    case 'convert_price_kilo_bmt':
      return convertPriceToKiloBMT(bid)
    case 'convert_price_gram_bmt':
      return convertPriceToGramBMT(bid)
    default:
      return '-'
  }
}

// Format functions
const formatAsk = (ask?: number): string => (ask ? pf(ask) : '-')

const formatAskBM = (ask?: number): string =>
  ask ? convert.priceBMToPound(ask) : '-'

const formatBidBM = (bid?: number): string =>
  bid ? convert.priceBMToPound(bid) : '-'

const formatBid = (bid?: number): string => (bid ? pf(bid) : '-')

const formatChange = (change: number, isBaseMetal: boolean): string =>
  change ? change.toFixed(isBaseMetal ? 3 : 2) : '-'

const formatChangePercentage = (changePercentage?: number): string =>
  changePercentage ? `${changePercentage.toFixed(2)}%` : '-'

const convertPriceToGram = (bid?: number): string =>
  bid ? convert.priceToGram(bid) : '-'

const convertPriceToKilo = (bid?: number): string =>
  bid ? convert.priceToKilo(bid) : '-'

const convertPriceToPennyweight = (bid?: number): string =>
  bid ? convert.priceToPennyweight(bid) : '-'

const convertPriceToTola = (bid?: number): string =>
  bid ? convert.priceToTola(bid) : '-'

const convertPriceToTael = (bid?: number): string =>
  bid ? convert.priceToTael(bid) : '-'

const convertChangeToKilo = (bid?: number, change?: number): string =>
  bid && change ? convert.priceChangeKilo(bid, change) : '-'

const convertChangeToGram = (bid?: number, change?: number): string =>
  bid && change ? convert.priceChangeGram(bid, change) : '-'

const convertChangeToPennyweight = (bid?: number, change?: number): string =>
  bid && change ? convert.priceChangePenny(bid, change) : '-'

const convertChangeToTola = (bid?: number, change?: number): string =>
  bid && change ? convert.priceChangeTola(bid, change) : '-'

const convertChangeToTael = (bid?: number, change?: number): string =>
  bid && change ? convert.priceChangeTael(bid, change) : '-'

const convertPriceToPoundBMT = (bid?: number): string =>
  bid ? convert.priceBMToPound(bid) : '-'

const convertPriceToTonBMT = (bid?: number): string =>
  bid ? convert.priceBMToTon(bid) : '-'

const convertPriceToTonneBMT = (bid?: number): string =>
  bid ? convert.priceBMToTonne(bid) : '-'

const convertChangeToTonBMT = (bid?: number, change?: number): string =>
  bid && change ? convert.priceBMTChangeTon(bid, change) : '-'

const convertChangeToTonneBMT = (bid?: number, change?: number): string =>
  bid && change ? convert.priceBMTChangeTonne(bid, change) : '-'

const convertChangeToKiloBMT = (bid?: number, change?: number): string =>
  bid && change ? convert.priceBMTChangeKilo(bid, change) : '-'

const convertChangeToGramBMT = (bid?: number, change?: number): string =>
  bid && change ? convert.priceBMTChangeGram(bid, change) : '-'

const convertPriceToKiloBMT = (bid?: number): string =>
  bid ? convert.priceBMToKilo(bid) : '-'

const convertPriceToGramBMT = (bid?: number): string =>
  bid ? convert.priceBMToGram(bid) : '-'
