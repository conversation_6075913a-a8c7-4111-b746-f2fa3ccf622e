import type { User, UserCredential } from 'firebase/auth'
import Image from 'next/image'
import type React from 'react'
import SignIn from '~/src/features/auth/signIn'
import { getUsername } from '~/src/services/firebase/service'

/**
 * SignInChooseProps interface
 *
 * @param onSuccess - Callback function to execute when login is successful
 * @param onLoginError - Callback function to execute when login fails
 */
interface SignInChooseProps {
  onLoginError: (error?: string) => void
  onLoginProcessStarted?: () => void
  onNeedUsername: (user: User) => void
  onSuccess: () => void
}

/**
 * LoginWithThirdProviders component
 *
 * @param onSuccess - Callback function to execute when login is successful
 * @param onLoginError - Callback function to execute when login fails
 * @param onNeedUsername
 * @param onLoginProcessStarted
 * @returns {React.ReactElement} - Rendered component
 */
function LoginWithThirdProviders({
  onLoginError,
  onLoginProcessStarted,
  onNeedUsername,
  onSuccess,
}: SignInChooseProps): React.ReactElement {
  /**
   * This function is used to handle the login with third party providers
   * It calls the handleSignInWithThirdProvider function
   * It then executes the onSuccess or onLoginError callback functions
   * based on the result of the login
   *
   * @param userCredential
   * @param provider
   */
  const handleSignInWithThirdProvider = async (
    provider: string,
  ): Promise<void> => {
    onLoginError()

    // Tell the parent component that the login process has started.
    onLoginProcessStarted()

    try {
      let userCredential: UserCredential = undefined

      // Select the provider to sign in
      if (provider === 'Google') {
        userCredential = await SignIn.signInWithGoogle()
      } else if (provider === 'Facebook') {
        userCredential = await SignIn.signInWithFacebook()
      }

      if (!userCredential) {
        onLoginError(`Failed to login with ${provider}. Please try again.`)
        return
      }

      // Check if user have a username
      if ((await getUsername(userCredential.user.email)) === null) {
        // If the user does not have a username, show a message to the user to set a username
        onNeedUsername(userCredential.user)

        return
      }

      onSuccess()
      onLoginError()
    } catch (error) {
      onLoginError(`Failed to login with ${provider}. Please try again.`)
      console.error(`Error trying to sign in with ${provider}:`, error)
    }
  }

  /**
   * This function is used to handle the login with Google
   * It calls the signInWithGoogle function from the Auth feature
   * It then executes the onSuccess or onLoginError callback functions
   * based on the result of the login
   *
   * @returns {Promise<void>}
   */
  const handleSignInWithGoogle = async (): Promise<void> => {
    await handleSignInWithThirdProvider('Google')
  }

  /**
   * This function is used to handle the login with Facebook
   * It calls the signInWithGoogle function from the Auth feature
   * It then executes the onSuccess or onLoginError callback functions
   * based on the result of the login
   *
   * @returns {Promise<void>}
   */
  const handleSignInWithFacebook = async (): Promise<void> => {
    await handleSignInWithThirdProvider('Facebook')
  }

  return (
    <>
      <div
        className="m-0 flex w-full flex-col
        items-center justify-between gap-4 border-0
        p-0 align-baseline"
      >
        <button
          role="button"
          type="button"
          className="relative flex w-full items-center
          justify-between gap-2 rounded-lg border
          border-slate-200
          px-4 py-2 text-kitco-black
          transition duration-150
          hover:border-slate-400 hover:text-slate-900 hover:shadow"
          onClick={handleSignInWithGoogle}
        >
          <Image
            className="h-6 w-6"
            src="/login/google-color.svg"
            loading="lazy"
            alt="Google logo"
            width={24}
            height={24}
          />
          <span>Signup or login with Google</span>
        </button>

        <button
          role="button"
          type="button"
          className="relative flex w-full items-center justify-between gap-2
          rounded-lg border border-slate-200 px-4 py-2 text-kitco-black
          transition duration-150 hover:border-slate-400 hover:text-slate-900
          hover:shadow"
          onClick={handleSignInWithFacebook}
        >
          <Image
            className="h-6 w-6"
            src="/login/facebook-color.svg"
            loading="lazy"
            alt="Facebook logo"
            width={24}
            height={24}
          />
          <span>Signup or login with Facebook</span>
        </button>
      </div>
    </>
  )
}

export default LoginWithThirdProviders
