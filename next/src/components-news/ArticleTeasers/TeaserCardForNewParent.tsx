import Link from 'next/link'
import type { FC } from 'react'
import { CategoryLink } from '~/src/components-news/ArticleTeasers/CategoryLink'
import { DateStamp } from '~/src/components-news/ArticleTeasers/DateStamp'
import { ImageMS } from '~/src/components/ImageMS/ImageMS.component'
import { Spacer } from '~/src/components/spacer/spacer.component'
import type { ArticlesUnion } from '~/src/types/types'
import cs from '~/src/utils/cs'

interface PropsTeaserCardForNewParent {
  node: ArticlesUnion
  size: 'sm' | 'md' | 'lg' | 'xl' | 'xxl'
  hideCategory?: boolean
  hideSummary?: boolean
  sizeImg?: 'sm' | 'md' | 'lg' | 'xl' | 'xxl'
  lineClamp?: string
}

export const TeaserCardForNewParent: FC<PropsTeaserCardForNewParent> = ({
  node,
  size,
  hideCategory,
  hideSummary,
  lineClamp,
}) => {
  const sizeCSS = {
    sm: 'text-[16px] leading-[130%]',
    md: 'text-[20px] leading-[130%] line-clamp-3',
    lg: 'text-[24px] leading-[130%]',
    xl: 'text-[34px] leading-[115%] tracking-[-0.0035em]',
  }

  const summaryCSS = {
    sm: '',
    md: '',
    lg: 'text-[16px] leading-[135%]',
    xl: 'text-[18px] leading-[135%]',
  }

  const spaceCSS = {
    sm: 'h-1',
    md: 'h-1',
    lg: 'h-1',
    xl: 'h-1',
  }

  const imgDimensions = {
    sm: { width: 304, height: 170 },
    md: { width: 774, height: 434 },
    lg: { width: 1540, height: 860 },
    xl: { width: 1543, height: 868 },
  }

  const heightImage = {
    sm: '',
    md: 'xl:h-[242px]',
    lg: '',
    xl: '',
  }

  return (
    <div className="w-full">
      <Link href={node?.urlAlias ?? '/'}>
        <ImageMS
          src={
            node?.teaserImage?.detail?.default?.srcset ??
            node?.image?.detail?.default?.srcset ??
            node?.legacyThumbnailImageUrl
          }
          hasLegacyThumbnailImageUrl={!!node?.legacyThumbnailImageUrl}
          alt={`${node?.title} teaser image`}
          priority={true}
          width={imgDimensions[size].width}
          height={imgDimensions[size].height}
          service="icms"
          className={cs([
            heightImage[size],
            'relative aspect-video w-full rounded-lg object-cover',
          ])}
        />
      </Link>
      <div className="h-4" />
      {!hideCategory && (
        <>
          <CategoryLink
            urlAlias={node?.category?.urlAlias}
            text={node?.category?.name}
          />
          <div className={cs(['bg-transparent', spaceCSS[size]])} />
        </>
      )}
      <Link href={node?.urlAlias ?? '/'}>
        <>
          <h3 className={cs([sizeCSS[size], 'text-kitco-black'])}>
            {node?.teaserHeadline ?? node?.title}
          </h3>
          {hideSummary ? null : (
            <div className="pt-2">
              <div
                className={cs(['summary', summaryCSS[size], lineClamp])}
                dangerouslySetInnerHTML={{ __html: node?.teaserSnippet }}
              />
            </div>
          )}
          <Spacer className="h-2" />
          <DateStamp stamp={node?.createdAt} />
        </>
      </Link>
    </div>
  )
}
