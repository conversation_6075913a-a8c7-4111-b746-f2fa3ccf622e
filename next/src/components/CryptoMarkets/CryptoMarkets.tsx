import clsx from 'clsx'
import Image from 'next/image'
import Link from 'next/link'
import type { CryptoComparePriceFull, CryptosTableQuery } from '~/src/generated'
import { cryptos } from '~/src/lib/cryptos-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import { colorizeTable } from '~/src/utils/colorize-change.util'
import { convertToLowerCase } from '~/src/utils/convertToLowerCase'
import priceFormatter from '~/src/utils/priceFormatter'
import styles from './CryptoMarkets.module.scss'

interface Item
  extends Pick<
    CryptoComparePriceFull,
    'fromSymbol' | 'changePct24HourCalculated' | 'imageUrl' | 'price'
  > {
  name: string
}
const items = [
  { id: 0, name: 'Bitcoin', symbol: 'BTC' },
  { id: 1, name: 'Ethereum', symbol: 'ETH' },
  { id: 2, name: 'X<PERSON>', symbol: 'XRP' },
]

export const cryptoMarketsVariables = {
  symbols: items.map((item) => item.symbol).join(','),
  currency: 'USD',
}

const CryptoMarkets = () => {
  const { data } = kitcoQuery(
    cryptos.cryptosTable({
      variables: cryptoMarketsVariables,
      options: {
        // @ts-ignore
        select: (res: CryptosTableQuery) => {
          const hashMap = new Map(items.map((item) => [item.symbol, item.name]))

          return res?.GetCryptoComparePriceFullV3?.map((item) => {
            if (!item?.price && !item?.fromSymbol) return null

            return {
              ...item,
              name: hashMap.get(item.fromSymbol),
            }
          }).filter(Boolean)
        },
      },
    }),
  )

  // typescript is mega upset about the transformation above, so let's just alias
  const transformedItems = data as Item[]

  return (
    <div className="relative border border-ktc-borders">
      <Link href="/price/crypto" className={'block h-[88px]'}>
        <div className={styles.ribbon}>
          <div className={styles.ribbonTopRight}>new</div>
        </div>
        <Image
          width={206}
          height={31}
          className="absolute left-[30px] top-[20px]"
          src={'/crypto-logo/crypto_logo-cropped.svg'}
          alt="Crypto Markets"
        />
      </Link>
      <Link href="/price/crypto">
        <div className={styles.title}>
          <h2 className="text-[16px] font-semibold capitalize leading-[25px]">
            {"Today's Crypto Markets"}
          </h2>
        </div>
      </Link>
      <div className={styles.content}>
        {transformedItems?.map((x) => (
          <div className={styles.topCrypto} key={x.fromSymbol}>
            <div className={styles.topCryptoTitle}>
              <img
                src={`${x.imageUrl}`}
                alt={x.name}
                className="h-[24px] w-[24px]"
              />
              <Link href={`/price/crypto/${convertToLowerCase(x.name)}`}>
                {x.name}
              </Link>
            </div>
            <div className={styles.topCryptoPrice}>
              {`$${priceFormatter(x.price)}`}
            </div>
            <div
              className={clsx(
                styles.topCryptoStats,
                colorizeTable(x.changePct24HourCalculated),
              )}
            >
              <span>{`${x.changePct24HourCalculated.toFixed(2)}%`}</span>
            </div>
          </div>
        ))}
      </div>
      <Link
        href="/price/crypto"
        target="_blank"
        className={clsx(
          'clear-both block h-auto items-center gap-2 border-t border-solid border-[#ccc] py-2.5 text-center text-[14px] font-bold no-underline opacity-100',
          'hover:text-[#c06a24] hover:underline',
        )}
      >
        More crypto markets and news...
      </Link>
    </div>
  )
}

export default CryptoMarkets
