import Link from 'next/link'
import type { FC } from 'react'
import { DateFormatter } from '~/src/components-news/ArticleTeasers/DateFormatter'
import css from '~/src/components-news/ArticleTeasers/article-teasers.module.scss'
import type { Category } from '~/src/generated'

export const CategoryAndTimeLink: FC<{
  category: Category
  date: string
}> = ({ category, date }) => {
  return (
    <Link className={css.desc} href={category?.urlAlias ?? '/'}>
      <>
        <span className="mr-4 text-sm text-ktc-category">
          {category?.name ?? 'Uncategorized'}
        </span>
        <time dateTime={date} className="text-xs text-ktc-date-gray">
          {DateFormatter(date)}
        </time>
      </>
    </Link>
  )
}
