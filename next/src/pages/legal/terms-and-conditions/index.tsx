import clsx from 'clsx'
import type { FC } from 'react'
import LayoutNoTopAdvertisement from '~/src/components/LayoutNoTopAdvertisement/LayoutNoTopAdvertisement'
import cs from '~/src/utils/cs'
import styles from './terms-and-conditions.module.scss'

const TermsAndConditions: FC<any> = () => (
  <LayoutNoTopAdvertisement title="Terms and conditions">
    <div className={clsx(styles.WordSection1)}>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '6.0pt',
          textAlign: 'left',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '28.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#09326C',
            fontWeight: 'bold',
          }}
        >
          Kitco Metals Inc.: Terms and Conditions
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '.0001pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          Disclaimer and transaction conditions for Kitco’s online store
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '.0001pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          Updated: January 1, 2025
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '6.0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          1. Definitions
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          For the purpose of these Terms and Conditions, the following terms
          shall have the meanings specified below, unless otherwise indicated:
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>“Agreement”</b> or <b>“Terms and Conditions”</b> shall mean these
          terms and conditions that shall govern the relationship between Kitco
          and the Customer, except as otherwise specifically provided for, and
          as same may be amended from time to time, without notice;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>&quot;Authorized User&quot;</b> shall mean a person to whom a Card
          has been issued at the authorization of the Primary Cardholder;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>“Business Day”</b> shall refer to Kitco’s operational hours,
          between 8:30 a.m. and 4:00 p.m., Monday through Friday, excluding
          Saturdays, Sundays and any statutory holidays and bank holidays in the
          province of Quebec;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>“Cleared Kitco Pool”</b> shall mean unallocated metal deposits of
          gold, silver, platinum, palladium, or rhodium, or such other metals as
          may be amended from time to time, calculated to three decimal places,
          that have been acquired or purchased from Kitco and that have been
          paid with cleared funds;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>
            “Cleared Kitco VaultChain<sup>TM</sup>”
          </b>{' '}
          shall mean unallocated metal deposits of gold and silver, calculated
          to three decimal places, that have been acquired or purchased from
          Kitco and that have been paid with cleared funds;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>“Customer”</b> shall mean a customer who enters into a transaction
          with Kitco for the purchase or sale of Precious Metal Bullion, Kitco
          Pool, Kitco VaultChain<sup>TM</sup> or Scrap Metal and is in
          compliance with these Terms and Conditions between the Customer and
          Kitco;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>“Customer Purchase Transaction”</b> shall mean any sale operation
          whereby Kitco sells Precious Metal Bullion, Precious Metal Products,
          Kitco Pool or Kitco VaultChain<sup>TM</sup> to its Customer;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>“Customer Sale Transaction”</b> shall mean any sale operation
          whereby Kitco purchases Precious Metal Bullion, Precious Metal
          Products, Kitco Pool or Kitco VaultChain<sup>TM</sup> from its
          Customer;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>“Kitco”</b> shall mean Kitco Metals Inc.;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>“Kitco Account”</b> shall mean a Customer account maintained with
          Kitco under the Customer’s name or any agreement, between Customer and
          Kitco, for the storage or custody of Precious Metals Products on an
          allocated or unallocated basis. The Kitco Account allows the Customer
          to place a Customer Purchase Transaction, Customer Sale Transaction or
          Scrap Metal Transaction for Precious Metals Products. The Kitco
          Account also allows the Customer access through Kitco’s Online Store
          to a web portal for the purposes of conducting transactions, viewing
          Customer’s account information, purchase and sale transactions history
          and account balance of cleared funds, stored Precious Metal Products,
          Kitco Pool and Kitco VaultChain<sup>TM</sup>;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>“Kitco’s Online Store”</b> shall mean and refer to Kitco’s Precious
          Metal Store, available at&nbsp;
        </span>
        <a href="https://online.kitco.com/">
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#95B3D7',
              textDecoration: 'underline',
            }}
          >
            https://online.kitco.com;
          </span>
        </a>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>“Kitco Pool”</b> shall mean unallocated metal deposits of gold,
          silver, platinum, palladium, or rhodium, or such other metals as may
          be amended from time to time, calculated to three decimal places,
          purchased through Kitco;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>
            “Kitco VaultChain<sup>TM</sup>”
          </b>{' '}
          shall mean metal deposits of gold and silver which are purchased
          through Kitco, calculated to three decimal places, securely stored by
          the Royal Canadian Mint and for which the chain of title is maintained
          and validated by TradeWind Markets, Inc. using a blockchain
          distributed ledger;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>“Open Transactions”</b> shall mean all contemplated and submitted
          Customer Purchase Transactions, Customer Sale Transactions and Scrap
          Metal Transactions that have not been completed. For the purposes of
          confirming a price for a transaction, a Customer Sale Transaction of
          Kitco Pool and Kitco VaultChain<sup>TM</sup> are not considered an
          Open Transaction;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>“Precious Metal(s)”</b> shall mean and refer to fine gold, silver,
          platinum, palladium, and rhodium or other such metals as may be
          amended from time to time;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>“Precious Metal Bullion”</b> shall mean and refer to Precious
          Metals in the form of bars, coins or rounds;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>“Precious Metal Products”</b> shall mean and refer to products in
          various forms which contain gold, silver, platinum or palladium or
          other such metals, including but not limited to Kitco Pool and Kitco
          VaultChain<sup>TM</sup>, as well as certain collectible products and
          accessories with varying precious metal purities, as may be amended
          from time to time;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>“Return of Funds”</b> shall mean and refer to a return of cleared
          funds held on account to the Customer;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>“Scrap Metal”</b> shall mean material containing gold, silver,
          platinum or palladium or other such metals as may be amended from time
          to time, which is suitable for melting or refining;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>“Scrap Metal Transaction”</b> shall mean any sale operation whereby
          Kitco purchases Scrap Metal from its Customer.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          The following Terms and Conditions shall govern all transactions
          between the Customer and Kitco, except when purchasing and selling
          specialized products such as Kitco Rhodium Sponge and Royal Canadian
          Mint Prestige Accounts. Kitco Rhodium Sponge transactions are subject
          to the&nbsp;
        </span>
        <a href="https://online.kitco.com/legal/terms-and-conditions">
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#95B3D7',
              textDecoration: 'underline',
            }}
          >
            Kitco Rhodium Sponge Product: Terms and Conditions
          </span>
        </a>
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          . Kitco Royal Canadian Mint Prestige Account transactions are subject
          to the RCMPA: Terms and Conditions.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          It is the Customer’s responsibility to confirm that their transaction
          conforms to the Terms and Conditions as set out in each of the above
          specialized products.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '6.0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          2. Application
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Customers wishing to purchase from or sell to Kitco must have an
          account. For Customers who currently have a Kitco Account, please
          refer to Section 3 of these Terms and Conditions.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          2.1.&nbsp;&nbsp;&nbsp;&nbsp;<u>Account Opening</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Depending on the type of account to be opened, individuals, business
          entities or trusts can create an account with Kitco online, by
          telephone or in person. Additional information regarding the
          application forms to be completed and the documentation requirements
          for each type of account can be found on Kitco’s website.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Failure to provide all required documentation may delay the opening of
          the Kitco Account. Upon receipt and formal acceptance of the completed
          application form and copies of the required documentation, Kitco will
          send a confirmation to the Customer as to whether the application has
          been approved or rejected. Once the application is approved, Kitco
          will open an account and inform the Customer of their account details.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          The Customer acknowledges that Kitco relies on the veracity of all
          information provided by the Customer when processing any application.
          By agreeing to these Terms and Conditions, the Customer represents and
          warrants to Kitco that all information provided by the Customer to
          Kitco is true and accurate.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>

      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          2.2.&nbsp;&nbsp;&nbsp;&nbsp;<u>Multiple Accounts</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Unless approved in advance by Kitco, only one account may be created
          per Customer. Kitco will not accept transactions placed by a single
          Customer over multiple accounts, and the Customer may be held liable
          for any resulting market loss repayments and administrative fees.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '6.0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          3. Transactions
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          3.1.&nbsp;&nbsp;&nbsp;&nbsp;<u>Transaction Restrictions</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco may restrict the purchase or sale of Precious Metal Bullion,
          Precious Metal Products or Scrap Metal, whether online or by
          telephone, as updated from time to time in the Schedule of Transaction
          Restrictions. Kitco reserves the right to unilaterally amend
          restrictions or implement new restrictions without notice at any time.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          3.2.&nbsp;&nbsp;&nbsp;&nbsp;<u>Customer Purchase Transactions</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '40.0pt',
          }}
        >
          3.2.1&nbsp;&nbsp;&nbsp;&nbsp;
          <u>Confirming a Price for Customer Purchase Transactions</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          A price is confirmed at the time a Customer Purchase Transaction is
          submitted (both online as well as by telephone). Should a Customer
          cancel a Customer Purchase Transaction after it has been submitted,
          the Customer may be liable for market loss repayments and
          administrative fees, pursuant to Sections 5.1 and 6.1.3. Subject to
          the conditions set out in the Schedule of Transaction Restrictions,
          the price at the time a transaction is submitted will be confirmed,
          regardless of market fluctuations.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          3.3.&nbsp;&nbsp;&nbsp;&nbsp;<u>Customer Sale Transactions</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '40.0pt',
          }}
        >
          3.3.1&nbsp;&nbsp;&nbsp;&nbsp;<u>Precious Metal Bullion</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Any Precious Metal Bullion sent to Kitco by mail must be accompanied
          by a packing slip. Customers wishing to confirm a price for their
          Customer Sale Transaction before sending their Precious Metal Bullion
          to Kitco may do so online or by contacting Kitco by telephone (see
          Section ******* for more details). Once a price is confirmed, Kitco
          will send Customer a packing slip by email.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Customers who do not wish to confirm a price prior to sending their
          Precious Metal Bullion to Kitco may create a packing slip online at
          Kitco’s Online Store.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco is not responsible for the customs clearing, duties, insurance
          and/or transportation costs related to any package. All costs relating
          to these issues remain Customer’s sole responsibility. Kitco does not
          provide any letters of guarantee or credit.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '80.0pt',
          }}
        >
          3.3.1.1&nbsp;&nbsp;&nbsp;&nbsp;<u>Testing Precious Metal Bullion</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Precious Metal Bullion sent to Kitco will be inspected, weighed and
          tested for purity and for authenticity. Depending on the type of
          product sent, destruct testing may be required. Should that be the
          case, Precious Metal Bullion will be damaged in the process of being
          tested. Customers wishing to have their Precious Metal Bullion
          returned after testing acknowledge and agree that it may not be
          returned to the Customer in the same condition as it was received at
          Kitco. Kitco may reject a package once it’s been tested if it’s
          determined that the Precious Metal Bullion sent by the Customer is
          tampered with, stolen or counterfeit, in which case market loss
          repayments and fees will apply, as described in Sections 5.4 and 6.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '80.0pt',
          }}
        >
          *******&nbsp;&nbsp;&nbsp;&nbsp;
          <u>
            Precious Metal Bullion that has been Tampered with or is Counterfeit
          </u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          In case of products which have been tampered with or counterfeit,
          Kitco may remit the products to local authorities for further
          investigation.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '80.0pt',
          }}
        >
          3.3.1.3&nbsp;&nbsp;&nbsp;&nbsp;
          <u>
            Confirming a Price for a Customer Sale Transaction of Precious Metal
            Bullion
          </u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          A price is confirmed at the time a Customer Sale Transaction for
          Precious Metal Bullion is submitted (online or by telephone). Should a
          Customer choose to send Precious Metal Bullion to Kitco along with a
          packing slip without confirming a price, their transaction will be
          completed at the market price once the package is received and
          processed.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Should a Customer cancel a Customer Sale Transaction after it has been
          submitted, the Customer may be liable for market loss repayments and
          administrative fees, pursuant to Sections 5 and 6.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          For Customer Sale Transactions where the Customer has chosen to
          confirm a price prior to sending Kitco the Precious Metal Bullion,
          Kitco guarantees the confirmed price on the packing slip created only
          when the Customer package is postmarked within one (1) Business Day of
          the date the order is submitted, and must be received within ten (10)
          Business Days.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          In the event the amount or type of Precious Metal Bullion received by
          Kitco differ from what was originally submitted by the Customer, the
          quoted price may be revised and any deficit amount will be treated as
          a cancellation. Market loss repayments and administrative fees will
          apply, as described in Sections 5 and 6. Should the Customer send
          other products in addition to those initially submitted, Customer will
          be contacted by Kitco in order to confirm a price for the additional
          products.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Subject to the conditions set out in the Schedule of Transaction
          Restrictions, the price at the time a transaction is submitted will be
          confirmed, regardless of market fluctuations.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '40.0pt',
          }}
        >
          3.3.2&nbsp;&nbsp;&nbsp;&nbsp;
          <u>
            Kitco Pool and Kitco VaultChain<sup>TM</sup>
          </u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          A price is confirmed at the time a Customer Sale Transaction for
          either Kitco Pool or VaultChain<sup>TM</sup> is submitted (both online
          as well as by telephone). Should a Customer cancel a Customer Sale
          Transaction after it has been transmitted, the Customer may be liable
          for market loss repayments and administrative fees, pursuant to
          Sections 5 and 6.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Customers must provide a valid Visa or Mastercard credit card in order
          to submit a transaction. The price at the time a transaction is
          submitted will be confirmed, regardless of market fluctuations.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          3.4.&nbsp;&nbsp;&nbsp;&nbsp;<u>Scrap Metal Transactions</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Customers may send or bring their Scrap Metal to Kitco to be tested,
          following which Customers may choose to sell said Scrap Metal to
          Kitco.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '40.0pt',
          }}
        >
          3.4.1&nbsp;&nbsp;&nbsp;&nbsp;<u>Sending Scrap Metal to Kitco</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Any Scrap Metal sent to Kitco by mail for testing or for a Scrap Metal
          Transaction must be accompanied by a packing slip. Customers wishing
          to confirm a price for their Scrap Metal Transaction before sending
          their Scrap Metal to Kitco may do so by contacting Kitco by telephone
          (see Section 3.4.3 for more details). Once a price is confirmed, Kitco
          will send Customer a packing slip by email. Customers who do not wish
          to confirm a price prior to sending their Scrap Metal to Kitco may
          create a packing slip online at Kitco’s Online Store. Customers must
          have a valid Kitco Refining Business account in order to create a
          packing slip.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco is not responsible for the customs clearing, duties, insurance
          and/or transportation costs related to any package. All costs relating
          to these issues remain Customer’s sole responsibility. Kitco does not
          provide any letters of guarantee or credit.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '40.0pt',
          }}
        >
          3.4.2&nbsp;&nbsp;&nbsp;&nbsp;<u>Testing Scrap Metal</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Scrap Metal sent to Kitco will be scratch tested using a touch stone
          and acid. Should it be impossible to determine the Precious Metal
          content of Customer’s Scrap Metal by this method, Customer will be
          contacted and may choose whether or not to proceed with testing using
          melt and/or assay services, subject to the applicable fees described
          in Section 6.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Scrap Metal will be damaged in the process of being tested/assayed. As
          a result, should Customer request that Scrap Metal be returned after
          testing, it may not be returned in the same condition as it was
          received at Kitco.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco&apos;s tests can accurately determine purity only up to 18
          Karats. Scrap Metal believed to be higher than 18 Karats must test as
          well as 18 Karats and have a recognized hallmark stamp. If the Scrap
          Metal scratch test at 18 Karats, but bears no stamp or an indistinct
          stamp, Kitco will purchase said Scrap Metal at the price applicable to
          18 Karat products. Customers may request an assay of their Scrap Metal
          at any time.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Once Customer’s Scrap Metal has been tested, Customer will be
          contacted to proceed with completing the Scrap Metal Transaction.
          Customers having had their Scrap Metal tested by Kitco may request
          that it be returned at any time before the transaction has been
          completed. Scrap Metal will be returned upon payment of relevant
          return shipping costs and any applicable market loss repayments and
          fees, as described in Sections 5 and 6.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco may reject a package once it has been tested if it has
          determined that the Scrap Metals sent by the Customer contains metal
          contaminants beyond certain acceptable limits. This Scrap Metal may be
          impossible to refine. In these cases, Kitco will return the Scrap
          Metal to the Customer, at the Customer’s expense.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco will also reject a package once it has been tested if it is
          suspected that the Scrap Metal sent by the Customer is fraudulent,
          counterfeit or has been stolen. Market loss repayments and fees will
          apply, as described in Sections 5.4 and 6.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '40.0pt',
          }}
        >
          3.4.3&nbsp;&nbsp;&nbsp;&nbsp;
          <u>Confirming a Price for Scrap Metal Transactions</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          A settlement price is confirmed at the time a Scrap Metal Transaction
          is submitted by telephone. This results in a binding agreement between
          Kitco and Customer relative to that transaction. Customers who choose
          to send Scrap Metal to Kitco along with a packing slip without
          confirming a price for the Scrap Metal Transaction beforehand will be
          contacted by Kitco once their package is received and tested in order
          to have their transaction completed.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          For Scrap Metal Transactions where the Customer has chosen to confirm
          a price for the transaction prior to sending Kitco their Scrap Metal,
          Kitco guarantees the price only when the Customer package is
          postmarked within two (2) Business Days of the date the order is
          submitted, when Customer provides Kitco with a tracking number for the
          package sent within two (2) Business Days of the date the order is
          submitted, and when the package is received within ten (10) Business
          Days. In the event the amount, karat weight or purity of the Scrap
          Metal received by Kitco differ from what was submitted by Customer,
          the quoted price may be revised and any deficit amount will be treated
          as a cancellation. Market loss repayments and administrative fees will
          apply, as described in Sections 5 and 6.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Should a Customer cancel a Scrap Metal Transaction after a price has
          been confirmed, or should Kitco reject a Customer package after a
          Scrap Metal Transaction has been submitted, the Customer may be liable
          for market loss repayments and administrative fees, pursuant to
          Sections 5 and 6.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Subject to the conditions set out in the Transaction Restrictions
          Schedule, the price at the time a transaction is submitted will be
          confirmed, regardless of market fluctuations.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          3.5.&nbsp;&nbsp;&nbsp;&nbsp;<u>Credit Cards</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          As described in Sections 3.2 and 3.3, Customers may be required to
          provide a valid Visa or Mastercard credit card when purchasing or
          confirming a price for a transaction.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Credit card is an acceptable form of payment for products purchased
          from Kitco (as outlined in Section 3.2) for market loss repayments (as
          outlined in Section 5) or other fees (as outlined in Section 6) that
          become payable and remain unpaid.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          By providing a valid credit card to Kitco, the Customer confirms that:
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
          marginLeft: '17.0pt',
          display: 'flex',
        }}
      >
        <span
          style={{
            fontSize: '10.0pt',
            fontFamily: 'Symbol',
            color: '#7F7F7F',
          }}
        >
          <span>
            •
            <span
              style={{
                fontSize: '7pt',
                fontFamily: 'Times New Roman',
              }}
            >
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </span>
          </span>
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          The Customer is the named credit card holder or Authorized User to use
          the credit card;
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        />
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
          marginLeft: '17.0pt',
          display: 'flex',
        }}
      >
        <span
          style={{
            fontSize: '10.0pt',
            fontFamily: 'Symbol',
            color: '#7F7F7F',
          }}
        >
          <span>
            •
            <span
              style={{
                fontSize: '7pt',
                fontFamily: 'Times New Roman',
              }}
            >
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </span>
          </span>
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          The credit card may be used to confirm a price for a transaction (if
          applicable);
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        />
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
          marginLeft: '17.0pt',
          display: 'flex',
        }}
      >
        <span
          style={{
            fontSize: '10.0pt',
            fontFamily: 'Symbol',
            color: '#7F7F7F',
          }}
        >
          <span>
            •
            <span
              style={{
                fontSize: '7pt',
                fontFamily: 'Times New Roman',
              }}
            >
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </span>
          </span>
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          The credit card may be used to pay for the Customer Purchase
          Transaction (as outlined in Section 3.2);
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        />
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
          marginLeft: '17.0pt',
          display: 'flex',
        }}
      >
        <span
          style={{
            fontSize: '10.0pt',
            fontFamily: 'Symbol',
            color: '#7F7F7F',
          }}
        >
          <span>
            •
            <span
              style={{
                fontSize: '7pt',
                fontFamily: 'Times New Roman',
              }}
            >
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </span>
          </span>
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco can pre-authorized a nominal amount on the credit card;{' '}
          <b>and</b>
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        />
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
          marginLeft: '17.0pt',
          display: 'flex',
        }}
      >
        <span
          style={{
            fontSize: '10.0pt',
            fontFamily: 'Symbol',
            color: '#7F7F7F',
          }}
        >
          <span>
            •
            <span
              style={{
                fontSize: '7pt',
                fontFamily: 'Times New Roman',
              }}
            >
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </span>
          </span>
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco may charge unpaid outstanding market loss repayments or other
          fees to the credit card.
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        />
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '6.0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          4. Payment and Funds on Account
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          4.1.&nbsp;&nbsp;&nbsp;&nbsp;<u>Funds on a Kitco Account</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Customers may add funds to their Kitco Account (also referred to as
          “funds on account”). Cleared funds on a Kitco Account shall be used
          towards payment for Customer Purchase Transactions, as described in
          Section 4.2, and may be used to confirm a price for both Customer
          Purchase Transactions and Customer Sale Transactions, as described in
          Sections 3.2 and 3.3. In order for funds on account to be considered
          cleared, there must be no hold placed on the funds, and applicable
          holding periods must have elapsed.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Funds on a Customer’s Kitco Account belong to the Customer until they
          are applied to a specific transaction or used as payment for any
          amount due to Kitco. These funds shall be specifically identified and
          segregated at all times from funds belonging to Kitco.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          4.2.&nbsp;&nbsp;&nbsp;&nbsp;
          <u>
            Acceptable Methods of Payment and Adding Funds to a Kitco Account
          </u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          At any one time, payment can be made and funds can be added to an
          account using only one method in addition to funds on account.
          Multiple payments or funds sent at the same time will not be accepted.
          The methods of payment or adding funds to a Kitco Account, along with
          their corresponding transaction limits and fees, are defined in the
          Schedule of Accepted Payment Methods.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco may refuse payments or funds (in any form whatsoever, including,
          and without limitation, credit card, checks, bank drafts, money orders
          or bank wires) received from an individual or from an entity that is
          not the named account owner. Furthermore, Kitco may request additional
          documentation before such payment or funds are applied to Customer’s
          Kitco Account or delivery of Customer&apos;s Purchase Transaction.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          4.3.&nbsp;&nbsp;&nbsp;&nbsp;
          <u>Payment Deadlines - Customer Purchase Transactions</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Payment must be sent to Kitco within one (1) Business Day from the
          date on which a transaction is submitted. Payments made by bank wire
          must be received by Kitco no later than three (3) Business Days from
          the date on which a transaction is submitted.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Should a Customer fail to send payment within one (1) Business Day
          from the date on which a transaction is submitted, or if Kitco does
          not receive payment within the deadline specified above, Kitco
          reserves the right to cancel the transaction and the Customer will be
          liable for market loss repayments and other fees, as described in
          Sections 5 and 6.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          4.4.&nbsp;&nbsp;&nbsp;&nbsp;<u>Credit Cards</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          A valid Visa or Mastercard credit card is an acceptable method of
          payment for Customers residing or having their principal place of
          business in Canada or the United States (subject to applicable
          restrictions as described in Section 4.2). If paying by credit card,
          Customer confirms that shipping address must match billing address
          associated with said card.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco may also refuse a payment by credit card without notice at any
          time and may request additional documentation and information before
          such payment is applied to Customer’s Purchase Transaction or delivery
          of the Customer&apos;s Purchase Transaction.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco does not accept any pre-paid credit cards or gift cards for a
          Customer’s Purchase Transaction.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          4.5.&nbsp;&nbsp;&nbsp;&nbsp;<u>Checks</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Checks are an acceptable method of payment or for adding funds to a
          Kitco Account for Customers residing or having their principal place
          of business in Canada or the United States (subject to applicable
          restrictions and holding periods as described in Section 4.2).
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Checks should be made payable to Kitco Metals Inc. Checks must always
          reference Customer’s Kitco Account in order for funds to be properly
          applied to said account.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Checks will be returned to the Customer by Kitco if they are
          incorrectly written (for reasons which include, without limitation,
          incorrect or missing date, dollar amount discrepancies, incorrect
          spelling of payee or missing signature). Incorrectly written checks
          will be returned to the Customer via regular mail once the Customer
          has been notified by Kitco of the returned check. Customers may choose
          to have their check returned via courier upon payment of a fee as
          defined in the Schedule of Accepted Payment Methods, including
          applicable taxes. Said fee can either be added to the amount of the
          replacement payment, or can be paid by credit card.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Checks that are returned by the bank in cases of fraud,
          counterfeiting, NSF (Non-Sufficient Funds) in Customer’s account, ACL
          (Account Closed) or CNT (Cannot Trace) will be subject to the
          administrative fee described in Section 6.1.2.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          4.6.&nbsp;&nbsp;&nbsp;&nbsp;<u>Bank Wires</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco’s bank wire information is made available to the Customer at the
          end of the online ordering process, through the Customer’s online
          account transaction details. This information is also made available
          to the Customer by e-mail, once a transaction is submitted.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Bank wire transfers must respect the payment deadlines described in
          Section 4.3. Bank wire transfers must always reference the Customer’s
          Kitco Account number and name in order for funds to be properly
          applied to said account.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '40.0pt',
          }}
        >
          4.6.1&nbsp;&nbsp;&nbsp;&nbsp;<u>International Bank Wires</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          International bank wires may be subject to correspondent bank fees
          that are deducted while a wire is in transit. As Kitco’s bank is
          simply the recipient bank, it is impossible to determine the fees that
          may be deducted by any correspondent banks. Kitco highly recommends
          that all Customers sending funds from a bank outside the United States
          and Canada include an additional amount on their bank wire to
          accommodate for any fees that may be deducted by a correspondent bank.
          The Customer should confirm with their banking institution the exact
          amount that may need to be included in the wire to cover all fees that
          may be deducted. Kitco will always credit the entire amount it
          receives to the Customer’s account. Should no fees be deducted, the
          entire bank wire amount will be credited to the Customer’s account.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          4.7.&nbsp;&nbsp;&nbsp;&nbsp;
          <u>
            Proceeds from Customer Sale Transactions, Scrap Metal Transactions
            and Return of Funds
          </u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Settlement options, along with the corresponding fees and transaction
          limits, for Customer Sale Transactions, Scrap Metal Transactions and
          Return of Funds are defined in the Schedule of Accepted Payment
          Methods and are subject to change from time to time.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Once a payment option has been selected or if the option to convert to
          Kitco Pool or VaultChain<sup>TM</sup> has been chosen, it cannot be
          altered for any reason.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Processing and settlement for Customer Sale Transactions and Scrap
          Metal Transactions may take two (2) Business Days from receipt of
          Precious Metal Bullion or Precious Metal Products by Kitco. Return of
          funds may take two (2) Business Days from receipt of request to Kitco.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          However, note that settlement time may be delayed due to statutory and
          bank holidays in Canada and in the US, increased market activity,
          capacity limitations of Kitco or size of the transaction.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Due to anti-money laundering legislation in both the United States and
          Canada, Kitco cannot forward the proceeds from any Customer Sale
          Transaction or Scrap Metal Transaction to an individual or entity who
          is not a designated account owner. Any payment from a Customer Sale
          Transaction or a Scrap Metal Transaction in a Business Account can be
          made in the business name only. For Sole Proprietorships, payment can
          be made in the business name or the sole proprietor’s name, provided
          proper authorization documentation has been received by Kitco.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Note that for any single transaction, payment can be made by only one
          method in addition to funds on account.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          4.8.&nbsp;&nbsp;&nbsp;&nbsp;<u>Completing a Transaction</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '40.0pt',
          }}
        >
          4.8.1&nbsp;&nbsp;&nbsp;&nbsp;<u>Customer Purchase Transactions</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          A Customer Purchase Transaction is completed when payment is received
          in full within the payment deadlines described in Section 4.2,
          provided that no hold is placed on payment, and Kitco performs its
          principal obligation.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          For Customer Purchase Transactions of Precious Metal Bullion,
          Kitco&apos;s principal obligation is presumed to have been performed
          if Kitco attempted to deliver the Precious Metal Bullion but was
          prevented from doing so by the actions or negligence of the Customer,
          or when Kitco consents to the Customer taking possession of the
          Precious Metal Bullion.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Legal ownership of the Precious Metal Bullion or Kitco Pool will be
          considered transferred from Kitco to the Customer once the transaction
          is completed.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '40.0pt',
          }}
        >
          4.8.2&nbsp;&nbsp;&nbsp;&nbsp;
          <u>Customer Sale Transactions and Scrap Metal Transactions</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          A Customer Sale Transaction or a Scrap Metal Transaction is completed
          when Kitco has received the Customer package within the deadlines
          specified in Section 3 and Kitco performs its principal obligation.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Legal ownership of the Customer package will be considered transferred
          from the Customer to Kitco once the transaction is completed.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          All Customer Sale Transactions and Scrap Metal Transactions are final.
          Once a transaction is complete and ownership has been transferred to
          Kitco, Precious Metal Bullion or Scrap Metal cannot be returned to
          Customer.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '6.0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          5. Transaction Cancellations
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          When cancelling a transaction, market loss repayments may apply as a
          submitted transaction constitutes a binding agreement between Kitco
          and the Customer. Applicable market loss repayments will be determined
          based on the following:
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          5.1.&nbsp;&nbsp;&nbsp;&nbsp;<u>Customer Purchase Transactions</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          In the event a Customer Purchase Transaction is cancelled (whether by
          the Customer or by Kitco for failure to receive payment in full within
          payment deadlines, as stipulated above), market loss repayments may be
          applicable. This fee is calculated based on whether there has been a
          decrease in the price of the Precious Metals (market loss) from the
          time the price is confirmed for a submitted transaction to the time a
          transaction is cancelled.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          For Customer Purchase Transactions cancelled by Kitco due to
          non-payment, market loss repayments will be calculated using the LBMA
          PM gold price, LBMA PM platinum price, LBMA PM palladium price or the
          LBMA silver price on the day that the payment deadline expires. For
          transactions involving rhodium, market loss repayments will be
          calculated using Kitco’s live spot price (plus applicable premiums)
          for rhodium on the day that the payment deadline expires.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          For Customer Purchase Transactions which are cancelled by the
          Customer, market loss repayments will be calculated using Kitco’s live
          spot price at the time a cancellation request is submitted.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>Decrease in Price of Precious Metals</b>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          In the event of a decrease in the price of Precious Metals, the market
          loss repayment will be equivalent to the difference between the
          original value of the Customer Purchase Transaction and its value at
          the time of cancellation.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>Increase in Price of Precious Metals</b>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          In the event of an increase in the price of Precious Metals, no market
          loss repayment will be applicable.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          5.2.&nbsp;&nbsp;&nbsp;&nbsp;
          <u>Customer Sale Transactions and Scrap Metal Transactions</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          In the event a Customer Sale Transaction or a Scrap Metal Transaction
          is cancelled, whether by the Customer or by Kitco, market loss
          repayments may be applicable. This fee is calculated based on whether
          there has been an increase in the price of Precious Metals from the
          time a transaction is submitted and the price confirmed to the time a
          transaction is cancelled.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Customer Sale Transactions for Precious Metal Bullion or Scrap Metal
          Transactions will be cancelled in ten (10) Business Days, if product
          is not received by one of our depositories. Customer must expedite
          shipment of any product included in their Customer Sale Transaction or
          their Scrap Metal Transaction within one (1) Business Day of the date
          the price is confirmed.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          For Customer Sale Transactions or Scrap Metal Transactions cancelled
          by Kitco, market loss repayments will be calculated using the LBMA PM
          gold price, LBMA PM platinum price, LBMA PM palladium price or the
          LBMA silver price on the day that the deadline for receiving Precious
          Metal Bullion or Scrap Metal expires. For transactions involving
          rhodium, market loss repayments will be calculated using Kitco’s live
          spot price for rhodium on the day the deadline for receiving Precious
          Metal Bullion or Scrap Metal expires.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>Increase in Price of Precious Metals</b>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          In the event of an increase in the price of Precious Metals, the
          market loss repayment will be equivalent to the difference between the
          original value of the Customer Sale Transaction or Scrap Metal
          Transaction and its value at the time of cancellation.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>Decrease in Price of Precious Metals</b>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          In the event of a decrease in the price of Precious Metals, no market
          loss repayment will be applicable.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          5.3.&nbsp;&nbsp;&nbsp;&nbsp;
          <u>Cancellation at the Customer&apos;s Request</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Transactions may be cancelled at the Customer’s request, provided
          payment has not been received or settlement has not been issued in
          full. Should the Customer request a cancellation, the above
          cancellation policy will be considered in effect. Fees will be
          calculated based on Kitco’s live spot price for the product being
          cancelled at the time a cancellation request is submitted.{' '}
          <b>
            Customers must submit all requests for cancellation by telephone
            only.
          </b>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          5.4.&nbsp;&nbsp;&nbsp;&nbsp;
          <u>
            Materials Sent Different from Those for Which Prices were Confirmed
          </u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Should the amount or product received by Kitco differ from that for
          which prices were confirmed by Customer, any deficit amount will be
          treated as a cancellation and the above cancellation policy will be
          considered in effect. Market loss repayments will be calculated using
          the LBMA PM gold price, LBMA PM platinum price, LBMA PM palladium
          price or the LBMA silver price on the day the deficit is discovered.
          For transactions involving rhodium, market loss repayments will be
          calculated using Kitco’s live spot price for rhodium on the day the
          deficit is discovered.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>Increase in Price of Precious Metals</b>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          In the event of an increase in the price of Precious Metals, the
          market loss repayment will be equivalent to the difference between the
          original confirmed price of the Customer Sale Transaction or the Scrap
          Metal Transaction and the price on the date the deficit is discovered
          by Kitco, multiplied by the number of pure ounces of which the
          Customer’s package was deficient.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>Decrease in Price of Precious Metals</b>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          In the event of a decrease in the price of Precious Metals, no market
          loss repayment will be applicable.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          5.5.&nbsp;&nbsp;&nbsp;&nbsp;
          <u>Unauthorized Sale Transaction Request</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          In the event that a Customer Sale Transaction is initiated when the
          Customer did not request the transaction, the Customer must notify
          Kitco immediately upon being made aware of the request so that Kitco
          may begin examination of the claim without delay. In the event that
          Kitco determines that the Customer Sale Transaction was unauthorized,
          the market loss repayments will be waived.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>

      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '6.0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          6. Fees
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco may charge the fees and premiums to the Customer in relation,
          but not limited to, Precious Metal Bullion, Precious Metal Products,
          Scrap Metal Transactions and other services. Such fees, which are
          subject to change from time to time, are set out in the Schedule of
          Fees and the amounts are posted on the Kitco Online Store (which fees
          shall be subject to change from time to time). Unless otherwise
          specified, all fees stated are in both US and Canadian Dollars. A
          transaction placed in British Pounds or Euros will be converted based
          on the US Dollar exchange rate at the time the transaction is
          submitted.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          6.1.&nbsp;&nbsp;&nbsp;&nbsp;<u>Administrative and Storage Fees</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '40.0pt',
          }}
        >
          6.1.1&nbsp;&nbsp;&nbsp;&nbsp;<u>Kitco Pool</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          An administrative fee in the amounts posted on the Kitco Online Store
          from time to time (which fees shall be subject to change) will be
          charged to Customers for all purchase and/or sale transactions that
          involve Kitco Pool. This includes transactions in which Kitco Pool is
          purchased or sold in combination with Precious Metal Bullion. The
          administrative fee is calculated/quoted in USD for U.S. dollar
          transactions, in CAD for Canadian dollar transactions and in USD
          equivalents for transactions in all other currencies.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '40.0pt',
          }}
        >
          6.1.2&nbsp;&nbsp;&nbsp;&nbsp;
          <u>
            Kitco VaultChain<sup>TM</sup>
          </u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco VaultChain<sup>TM</sup> accounts may be subject to an applicable
          account opening fee, administrative fees, account fees and storage
          fees in the amounts posted on the Kitco Online Store from time to time
          (which fees shall be subject to change). Applicable Kitco VaultChain
          <sup>TM</sup> account fees will be charged to Customers upon the
          opening of a Kitco VaultChain<sup>TM</sup>
          &nbsp;account and on a recurring basis thereafter.
        </span>
      </p>
      {/* line break start */}
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      {/* line break end */}
      {/* new paragraph */}
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Applicable VaultChain<sup>TM</sup> Storage Fees will be charged to
          Customers on a recurring basis and are based on the value of the
          VaultChain<sup>TM</sup> holdings being stored.
        </span>
      </p>
      {/* end paragraph */}
      {/* line break start */}
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      {/* line break end */}
      {/* new paragraph */}
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          The account opening fee, administrative fees, account fees and storage
          fees are calculated/quoted in USD for U.S. dollar transactions, in CAD
          for Canadian dollar transactions and in USD equivalents for
          transactions in all other currencies.
        </span>
      </p>
      {/* end paragraph */}
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '40.0pt',
          }}
        >
          6.1.3&nbsp;&nbsp;&nbsp;&nbsp;<u>Returned Check</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          An administrative fee will be charged to Customer if a check (of any
          kind), bank draft or money order that Customer has sent to Kitco has
          been returned. This includes, but is not limited to, checks returned
          due to fraud, NSF (Non-Sufficient Funds) in Customer’s account, ACL
          (Account Closed) or CNT (Cannot Trace).
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '40.0pt',
          }}
        >
          6.1.4&nbsp;&nbsp;&nbsp;&nbsp;<u>Transaction Cancellations</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          In addition to applicable market loss repayments, transactions
          cancelled pursuant to Section 5 will be subject to an administrative
          fee in the amounts posted on the Kitco Online Store from time to time
          (which fees shall be subject to change).
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '40.0pt',
          }}
        >
          6.1.5&nbsp;&nbsp;&nbsp;&nbsp;<u>Changes to Delivery Address</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          An administrative fee in the amounts posted on the Kitco Online Store
          from time to time (which fees shall be subject to change), will be
          charged in the event the Customer wishes to make any changes to the
          delivery address for the Purchase Transaction. Any request for change
          is subject to Kitco&apos;s approval and Kitco reserves the right to
          refuse the Customer&apos;s request for any reason.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '40.0pt',
          }}
        >
          6.1.6&nbsp;&nbsp;&nbsp;&nbsp;<u>Undeliverable Packages</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          An administrative fee in the amounts posted on the Kitco Online Store
          from time to time (which fees shall be subject to change), will be
          charged to Customer in the event that Customer’s Precious Metal
          Bullion is bought back by Kitco as a result of said Precious Metal
          Bullion being returned as undeliverable, further to Section 7 herein.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          6.2.&nbsp;&nbsp;&nbsp;&nbsp;<u>Fabrication Fees</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          The Customer may request a conversion from Cleared Kitco Pool or
          Cleared Kitco VaultChain<sup>TM</sup> to any corresponding Precious
          Metal Bullion product. In these cases, a fabrication fee may be
          applicable. Said fee may vary, depending on the physical product the
          Customer requests. Shipping and insurance fees (as outlined in
          sub-section 6.3), or pick-up fees (as outlined in sub-section 6.4),
          may be incurred.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          6.3.&nbsp;&nbsp;&nbsp;&nbsp;<u>Shipping and Insurance Fees</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Should the Customer request that Kitco arrange for shipping or
          delivery of Precious Metal Bullion, or should the Customer request or
          Kitco be required to return any Precious Metal Bullion or Scrap Metal
          back to the Customer, shipping and insurance fees will be applicable.
          Quotes for shipping and insurance fees will be provided during the
          transaction process based on the value of the shipment, the type of
          products being shipped, the shipping method and the shipping
          destination. Taxes may apply to shipping and insurance fees. For
          Shipments outside of continental United States and Canada, standard
          shipping rates may not apply.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          All shipments prepared by Customer for delivery to Kitco must be
          properly packaged and labeled in order to accelerate processing times
          and ensure the safe arrival of Customer packages. Detailed shipping
          and packaging instructions can be found on Kitco’s Online Store.
          Failure to properly package and label shipments sent to Kitco may
          result in the insurer rendering insurance coverage void.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '40.0pt',
          }}
        >
          6.3.1&nbsp;&nbsp;&nbsp;&nbsp;<u>Large Shipments</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          For certain large shipments, standard shipping and insurance rates may
          not apply. Large shipments may include (without limitation) orders
          over $100,000.00 in value, order containing 100 oz / 400 oz gold bars,
          monster boxes or orders over 700 oz in total weight. For large
          shipments, a quote for shipping and insurance fees will be provided by
          Kitco on a case-by-case basis.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Furthermore, Kitco may recommend or require shipment via armored
          vehicle, which will require delivery to a business, bank, or
          depository service. If applicable, the Customer is responsible for all
          shipping and insurance costs for armored vehicle service.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          6.4.&nbsp;&nbsp;&nbsp;&nbsp;<u>Pick-up and Drop-off Fees</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Should the Customer submit a Purchase Transaction or convert to
          Precious Metal Bullion and pick-up at one of Kitco’s depositories, a
          pick-up fee may be applicable, dependent on location. Furthermore, a
          fee may be charged to Customers who drop off items at one of Kitco’s
          depositories in relation to a Customer Sale Transaction, dependent on
          location. Such fees will be charged based on the amounts posted on the
          Kitco Online Store from time to time (which fees shall be subject to
          change)
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          6.5.&nbsp;&nbsp;&nbsp;&nbsp;<u>Melt & Assay Fees</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          If a Kitco representative can test Customer’s Scrap Metal with a
          scratch test, there will be no processing fees. However, if determined
          that a melt and/ or assay are required, Kitco will contact Customer
          before proceeding to advise the Customer of fees and processing times.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          6.6.&nbsp;&nbsp;&nbsp;&nbsp;
          <u>Handling Fees for Testing Scrap Metal</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          In addition to applicable market loss repayments and the
          administrative fee described in Section 6.1.3, Customers who confirm a
          price with Kitco and then decide to not sell the Scrap Metal after it
          has been tested, will be charged a handling fee of $70 (USD for U.S.
          dollar transactions, in CAD for Canadian dollar transactions and in
          USD equivalents for transactions in all other currencies). Said
          handling fee shall also apply should Customer simply send Scrap Metal
          to Kitco for testing without completing a Scrap Metal Transaction.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          6.7.&nbsp;&nbsp;&nbsp;&nbsp;<u>Taxes and Duties</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Should the Customer request delivery of Precious Metal Bullion or
          should any package be returned to a location outside the United States
          or Canada, taxes and duties, including Value Added Taxes (VATs), may
          be applicable, depending on the country of import. The Customer, in
          agreeing to these Terms and Conditions, acknowledges that any taxes or
          duties, including VATs, will be the sole responsibility of the
          Customer and Kitco will not be held liable for any fees.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          6.8.&nbsp;&nbsp;&nbsp;&nbsp;<u>Sales Tax</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Unless otherwise indicated in this Agreement, all fees quoted herein
          and on Kitco&apos;s Online Store are exclusive of sales tax.
          Applicable sales tax will be added to Customer&apos;s Purchase
          Transaction depending on Customer&apos;s delivery address. For more
          information, please contact Kitco.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          6.9.&nbsp;&nbsp;&nbsp;&nbsp;<u>Undeliverable or Refused Packages</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          In the event that a Customer has asked Kitco to arrange for delivery
          of Precious Metal Bullion or Precious Metal Products and that said
          Precious Metal Bullion or Precious Metal Products returned to Kitco
          for any reason, including, without limitation that the package has
          been deemed by the carrier to be undeliverable or are refused by the
          recipient, Kitco will promptly notify the Customer in order to obtain
          further shipping instructions. Once instructions have been received
          from the Customer, Kitco will arrange for the Customer’s Precious
          Metal Bullion or Precious Metal Products to be re-shipped and shipping
          and insurance fees will be applicable as described in Section 6.3.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Instructions regarding the re-shipment of returned Precious Metal
          Bullion or Precious Metal Products must be received from the Customer
          no later than ten (10) Business Days from the date on which the
          Customer was notified by Kitco of the return.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Should the Customer fail to contact Kitco regarding re-shipment of the
          Precious Metal Bullion or Precious Metal Products, or if Kitco is
          unable to arrange for re-shipment of the returned Precious Metal
          Bullion or Precious Metal Products within the deadline specified
          herein, Kitco will proceed to buy back said Precious Metal Bullion or
          Precious Metal Products. In these cases, an administrative fee will
          apply, as described in Section 6.1.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Precious Metal Bullion will be bought back by Kitco using the LBMA PM
          gold price, LBMA PM platinum price, LBMA PM palladium price or the
          LBMA silver price on the date that the deadline for receiving shipping
          instructions from the Customer expires, plus applicable premiums. For
          transactions involving rhodium, the price used will be Kitco’s live
          spot price for rhodium on the day the deadline for receiving shipping
          instructions from the Customer expires, plus applicable premiums.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Proceeds from the sale of returned Precious Metal Bullion or Precious
          Metal Products will be deposited as funds on Customer’s Kitco Account,
          less any applicable fees.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '6.0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          7. Exchange Policy
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Customer may ask for the exchange of any Precious Metal Bullion or
          Precious Metal Products purchased from Kitco in the event that the
          Customer is unsatisfied with said products. Customer must notify Kitco
          by telephone of their desire to exchange their product(s) within three
          (3) Business Days of having received said product(s) from Kitco.
          Products must be returned in their original packaging and all shipping
          and insurance costs for incoming and outgoing shipments shall be at
          the expense of the Customer.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          All requests for exchange are subject to Kitco’s approval and Kitco
          reserves the right to reject a Customer&apos;s request for exchange
          for any reason including, without limitation, lack of availability of
          the Precious Metal Bullion or Precious Metal Products in question.
          Additional costs may be applicable in case of an exchange for a
          product with a higher premium.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '6.0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          8. Return Policy
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Customer may ask to return any Precious Metal Bullion or Precious
          Metal Products purchased from Kitco in the event that the Customer is
          unsatisfied with said products. All returns will be purchased from
          Customer as a Sale Transaction as outlined in Section 3.3 less any
          applicable market loss repayment.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '6.0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          9. Right to Withholding and Compensation (Set-off)
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          To secure the payment or repayment of any sums payable by Customer to
          Kitco, including, without limitation, for transactions, market loss
          repayments, account opening fees, administrative fees, storage fees,
          or any other account or transaction-related fees, Customer undertakes
          to maintain and consents that Kitco withholds cleared funds on
          Customer’s Kitco Account, Precious Metal Products, Cleared Kitco Pool
          or Cleared Kitco VaultChain<sup>TM</sup>, in an amount corresponding
          to those funds needed to confirm a price for Customer transactions, as
          described in Section 3. Kitco may withhold said funds on Customer’s
          Kitco Account, Cleared Kitco Pool or Cleared Kitco VaultChain
          <sup>TM</sup> until full payment has been received.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          In case of cancellation of a transaction or non-payment by Customer of
          any amounts due and payable to Kitco pursuant to the terms of this
          Agreement, Kitco may, subject to applicable laws and the terms of this
          Agreement (including in the case of overdue amounts as described in
          Section 13.1), in its discretion and without notice to Customer,
          set-off or compensate any amounts that Customer owes to Kitco against
          any of the cleared funds or other assets in Customer’s Kitco Account
          including but not limited to cleared funds, Precious Metal Products,
          Cleared Kitco Pool and/or Cleared Kitco VaultChain<sup>TM</sup>.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          To the extent allowable by applicable law, Kitco may enforce its
          rights under this Section 9 by: (a) using any cleared funds in a Kitco
          Account to satisfy such outstanding amounts; or (b) disposing of any
          assets held in a Kitco Account including but not limited to balances
          of Precious Metal Products, Cleared Kitco Pool or Cleared Kitco
          VaultChain<sup>TM</sup> to satisfy such outstanding amounts. In the
          event that Kitco disposes of any of the assets held in the Customer’s
          Kitco Account in accordance with its rights under this Section 9,
          then, after deducting all unpaid amounts due and owing to Kitco plus
          any and all costs of administration, including any costs associated
          with storing, disposing and reselling the assets, and any other
          charges, fees and expenses Kitco may incur in disposing of the assets
          and enforcing its rights hereunder. Kitco shall remit the remaining
          balance, if any, of the proceeds of such disposition to the Customer.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Customer acknowledges and agrees that it will reimburse Kitco for all
          reasonable costs incurred by Kitco (including legal fees) in
          connection with the collection of any amounts owed by Customer to
          Kitco.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '6.0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          10. Kitco Pool
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          As defined in the present Agreement, Kitco Pool is comprised of
          unallocated precious metal deposits purchased through Kitco. Kitco
          Pool may consist of precious metals in any form whatsoever, including,
          without limitation, bars, coins, and scrap metal. Precious metal
          deposits which form Kitco Pool shall remain the property of Customers
          holding Kitco Pool at all times and Kitco shall cause such precious
          metal deposits to be specifically identified and segregated at all
          times from coin, bullion and metal in any form whatsoever which belong
          to any other person or entity, including Kitco. These precious metal
          deposits belong to Customers who hold Kitco Pool and are available to
          said Customers at any time, subject to payment of related fees and the
          time necessary for fabrication and delivery. Kitco Pool is stored in
          secured depositories in various locations. Said depositories are
          chosen by Kitco at its sole discretion.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Customers holding Kitco Pool hereby authorize Kitco to perform any and
          all acts necessary for the administration of Kitco Pool on their
          behalf, including, without limitation, arranging for the insurance of
          Kitco Pool, storing and transferring unallocated metals from one
          depository to another and having scrap metal deposits refined.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          A Customer owning Cleared Kitco Pool may request a conversion from
          Kitco Pool to the corresponding Precious Metal Bullion product. For
          conversions from Precious Metal Bullion to Kitco Pool, the Customer
          will be credited with the exact ounce amount of the product converted.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '6.0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          11. Kitco VaultChain<sup>TM</sup>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          As defined in the present Agreement, Kitco VaultChain<sup>TM</sup> is
          comprised of unallocated gold and silver deposits purchased through
          Kitco for which the chain of title is recorded in a blockchain
          distributed ledger maintained by TradeWind Markets, Inc. Kitco
          VaultChain<sup>TM</sup> may consist of precious metals in any form
          whatsoever, including, without limitation, bars, coins, and scrap
          metal. Precious metal deposits that form Kitco VaultChain<sup>TM</sup>{' '}
          shall remain the property of Customer holding Kitco VaultChain
          <sup>TM</sup> at all times. These precious metal deposits belong to
          Customers who hold Kitco VaultChain<sup>TM</sup> and are available to
          said Customers at any time, subject to payment of related fees and the
          time necessary for fabrication and delivery. The Royal Canadian Mint
          securely stores Kitco VaultChain<sup>TM</sup>.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Customers holding Kitco VaultChain<sup>TM</sup> hereby authorize Kitco
          to perform any and all acts necessary for the administration of Kitco
          VaultChain<sup>TM</sup> on their behalf, including, without
          limitation, arranging for the insurance of Kitco VaultChain
          <sup>TM</sup>.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          A Customer owning Cleared Kitco VaultChain<sup>TM</sup> may request a
          conversion from Kitco VaultChain<sup>TM</sup> to the corresponding
          Precious Metal Bullion product. For conversions from Precious Metal
          Bullion to Kitco VaultChain<sup>TM</sup>, the Customer will be
          credited with the exact ounce amount of the product converted, in
          accordance with Section 6.2 of this present Agreement.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '6.0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          12. Melt Loss of Scrap Metal
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Testing and refining Scrap Metal will result in a decrease in the
          gross weight of said materials due to the burning process in which
          impurities such as alloys are eliminated. A decrease in weight may
          also occur as gemstones, enamel or other imbedded metals are separated
          and retained in borax. While the average loss in purely metallic
          material (without gemstones) is approximately 1%, this will increase
          if Customer’s Scrap Metal includes gemstones or other non-precious
          metal content. Dental Scrap Metal such as gold filings may lose up to
          30% of their weight, depending on the amount of contamination present
          in said materials. To aid in melting of substances containing
          palladium or platinum, copper may need to be added to melts. In such
          cases, the melt loss is difficult to estimate and will vary from cases
          to case.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '6.0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          13. Remedies
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          In the event that Customer is in default of any of its obligations
          under this Agreement, Kitco shall immediately have the option, in its
          sole discretion, in addition to any other rights which it may have at
          law or in equity and without any further notice to the Customer, to
          exercise any of the remedies specified under this Agreement.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          13.1.&nbsp;&nbsp;&nbsp;&nbsp;<u>Overdue Amounts</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          In the event that Customer fails to pay any amount due and owing at
          any time to Kitco in accordance with the terms of this Agreement,
          Kitco may provide Customer with written notice detailing the failure
          to pay and Kitco’s intention to dispose of the Customer’s Precious
          Metal Products (including Cleared Kitco Pool and/or Cleared Kitco
          VaultChain<sup>TM</sup> balances) if the failure is not remedied
          within sixty (60) days following the delivery of such notice. In the
          event that Customer fails to remedy its failure pay such outstanding
          amounts in full within said sixty (60) day period, Kitco shall be
          entitled to dispose of the Precious Metal Products (including Cleared
          Kitco Pool and/or Cleared Kitco VaultChain<sup>TM</sup> balances) in
          its sole and entire discretion, without further notice or delay. In
          the event that Kitco sells the Customer’s Precious Metal Products
          (including Cleared Kitco Pool and/or Cleared Kitco VaultChain
          <sup>TM</sup> balances) in accordance with its rights hereunder, as
          are more specifically set out in Section 9 above.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          13.2.&nbsp;&nbsp;&nbsp;&nbsp;<u>Inability to Perform</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          In the event that:
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
          marginLeft: '17.0pt',
          display: 'flex',
        }}
      >
        <span
          style={{
            fontSize: '10.0pt',
            fontFamily: 'Symbol',
            color: '#7F7F7F',
            display: 'inline-block',
            minWidth: '40px',
          }}
        >
          <span>
            (i)
            <span
              style={{
                fontSize: '7pt',
                fontFamily: 'Times New Roman',
              }}
            >
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </span>
          </span>
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          either Kitco or Customer fails to perform any material obligation
          pursuant to the terms of this Agreement and does not cure such failure
          within thirty (30) days after the receipt of written notice thereof
          from the other party,
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        />
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
          marginLeft: '17.0pt',
          display: 'flex',
        }}
      >
        <span
          style={{
            fontSize: '10.0pt',
            fontFamily: 'Symbol',
            color: '#7F7F7F',
            display: 'inline-block',
            minWidth: '40px',
          }}
        >
          <span>
            (ii)
            <span
              style={{
                fontSize: '7pt',
                fontFamily: 'Times New Roman',
              }}
            >
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </span>
          </span>
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          either Kitco or Customer shall be dissolved or adjudged bankrupt, or a
          trustee, receiver or conservator of such party or its property shall
          be appointed, or an application for any of the foregoing is filed,
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        />
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
          marginLeft: '17.0pt',
          display: 'flex',
        }}
      >
        <span
          style={{
            fontSize: '10.0pt',
            fontFamily: 'Symbol',
            color: '#7F7F7F',
            display: 'inline-block',
            minWidth: '40px',
          }}
        >
          <span>
            (iii)
            <span
              style={{
                fontSize: '7pt',
                fontFamily: 'Times New Roman',
              }}
            >
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </span>
          </span>
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          control of either Kitco or Customer is taken over by any government or
          other public authority, or
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        />
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
          marginLeft: '17.0pt',
          display: 'flex',
        }}
      >
        <span
          style={{
            fontSize: '10.0pt',
            fontFamily: 'Symbol',
            color: '#7F7F7F',
            display: 'inline-block',
            minWidth: '40px',
          }}
        >
          <span>
            (iv)
            <span
              style={{
                fontSize: '7pt',
                fontFamily: 'Times New Roman',
              }}
            >
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </span>
          </span>
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          any government or governmental agency shall have taken any action
          which has materially adversely affected or will materially adversely
          affect a party’s ability to perform any of its obligations hereunder,
          and such action shall not have been rescinded or modified, and the
          adverse effects thereof shall not have been eliminated, within thirty
          (30) days after written notice of such action shall have been given to
          the other party,
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        />
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          then this Agreement may be terminated at any time thereafter by Kitco
          or Customer upon written notice to the other party.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          In the event of termination of this Agreement in accordance herewith,
          provided that there are no unpaid fess, charges or other amounts due
          to Kitco, Kitco shall promptly arrange for the delivery of all
          Precious Metal held for Customer in accordance with Customer’s
          instructions.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '6.0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          14. Risk
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          14.1.&nbsp;&nbsp;&nbsp;&nbsp;<u>Advice</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco has not offered or given and will not provide any investment
          advice in connection with any transaction and has not offered or given
          any opinion with respect to the suitability of any transaction made or
          which might be made by the Customer.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          The Customer acknowledges, represents and warrants that Kitco has not
          offered or given any investment advice to Customer in connection with
          the products and services offered by Kitco. In addition, Kitco has not
          given Customer any opinion with respect to the suitability of any of
          its products or services for Customer.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          14.2.&nbsp;&nbsp;&nbsp;&nbsp;<u>Market Risk</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Customers should carefully consider the suitability of Precious Metals
          as an investment choice before taking any decisions that may affect
          their financial situation. Precious Metal products and accounts are
          not insured by the Canadian Deposit Insurance Corporation, Canadian
          Investor Protection Fund or other similar program and may lose value.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          The purchase and sale of Precious Metals involves a high degree of
          risk and is not suitable for all persons. The purchase and sale of
          Precious Metals provides no guarantee of interest, yield or return.
          Losses may be incurred both as a result of price devaluation and if
          price gains do not exceed applicable fees, including those charged
          herein. The Customer has read and understands these Terms and
          Conditions and acknowledges that all risk of decline in the market
          value of any Precious Metals is the Customer’s risk and not that of
          Kitco. If the Customer has any doubt as to the suitability of Precious
          Metals as an investment, the Customer should contact an independent
          legal or financial advisor.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '6.0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          15. Kitco’s Rights
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco hereby reserves the following rights and the Customer
          acknowledges and agrees to the following:
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
          marginLeft: '17.0pt',
          display: 'flex',
        }}
      >
        <span
          style={{
            fontSize: '10.0pt',
            fontFamily: 'Symbol',
            color: '#7F7F7F',
          }}
        >
          <span>
            •
            <span
              style={{
                fontSize: '7pt',
                fontFamily: 'Times New Roman',
              }}
            >
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </span>
          </span>
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco may adjust ASK and BID prices at any time without prior notice.
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        />
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
          marginLeft: '17.0pt',
          display: 'flex',
        }}
      >
        <span
          style={{
            fontSize: '10.0pt',
            fontFamily: 'Symbol',
            color: '#7F7F7F',
          }}
        >
          <span>
            •
            <span
              style={{
                fontSize: '7pt',
                fontFamily: 'Times New Roman',
              }}
            >
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </span>
          </span>
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco may withdraw or limit the products or services provided by
          telephone, in person and on Kitco’s Online Store, or may suspend the
          availability of Kitco’s Online Store, or suspend the completion of
          transactions submitted to Kitco, without notice at any time.
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        />
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
          marginLeft: '17.0pt',
          display: 'flex',
        }}
      >
        <span
          style={{
            fontSize: '10.0pt',
            fontFamily: 'Symbol',
            color: '#7F7F7F',
          }}
        >
          <span>
            •
            <span
              style={{
                fontSize: '7pt',
                fontFamily: 'Times New Roman',
              }}
            >
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </span>
          </span>
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco may cancel or reject any order at any time.
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        />
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
          marginLeft: '17.0pt',
          display: 'flex',
        }}
      >
        <span
          style={{
            fontSize: '10.0pt',
            fontFamily: 'Symbol',
            color: '#7F7F7F',
          }}
        >
          <span>
            •
            <span
              style={{
                fontSize: '7pt',
                fontFamily: 'Times New Roman',
              }}
            >
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </span>
          </span>
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco may place limits on the amounts that may be offered for sale or
          the amounts that may be bought back by Kitco in any twenty-four (24)
          hour period.
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        />
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
          marginLeft: '17.0pt',
          display: 'flex',
        }}
      >
        <span
          style={{
            fontSize: '10.0pt',
            fontFamily: 'Symbol',
            color: '#7F7F7F',
          }}
        >
          <span>
            •
            <span
              style={{
                fontSize: '7pt',
                fontFamily: 'Times New Roman',
              }}
            >
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </span>
          </span>
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco may terminate this Agreement at its sole discretion and may
          freeze an account or liquidate and close an account at any time for
          any reason including but not limited to the abuse of policy, criminal
          activity, circumvention of Kitco’s web security services, and any
          other violation of these Terms and Conditions. If the Customer has
          Kitco Pool, Kitco will close the Customer’s Kitco Account and
          liquidate any Kitco Pool holdings and send the Customer a check or
          bank wire as explained in sub-section 4.6 herein.
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        />
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '6.0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          16. Limitation of Liability
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco hereby reserves the following rights and the Customer
          acknowledges and agrees to the following:
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          16.1.&nbsp;&nbsp;&nbsp;&nbsp;<u>Capital Gains/Income Taxes</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco does not report any Customer transactions to the Internal
          Revenue Service or the Canada Revenue Agency; however, the purchase or
          sale of Precious Metals may be considered reportable as taxable
          income. It is the Customer’s responsibility to contact a financial
          advisor for further information. Kitco will not be held liable for any
          capital gains or tax implications due to the purchase or sale of
          precious metals by the Customer.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          16.2.&nbsp;&nbsp;&nbsp;&nbsp;<u>Technical Issues</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '40.0pt',
          }}
        >
          16.2.1&nbsp;&nbsp;&nbsp;&nbsp;
          <u>Electronic Order Entry Risk Disclosure and Disclaimer</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco specifically disclaims any liability or responsibility for
          orders placed via the Kitco online order entry system, for any losses,
          or direct, indirect, consequential or incidental damages, which the
          Customer may recognize or incur as a result of the use of the Kitco
          online order entry system. Further, Kitco specifically disclaims any
          liability for the interruption, cancellation or other termination of
          the Kitco online order entry system.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '40.0pt',
          }}
        >
          16.2.2&nbsp;&nbsp;&nbsp;&nbsp;<u>Negligence</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          All orders placed through the order entry system are taken on a best
          efforts basis. Kitco shall not be responsible for errors, negligence
          or inability to execute orders. Nor shall Kitco be responsible for any
          delays in the transmission, delivery or execution of Customer&apos;s
          order due to breakdown or failure of transmission or communication
          facilities, or to any other cause or causes beyond Kitco&apos;s
          reasonable control or anticipation.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '40.0pt',
          }}
        >
          16.2.3&nbsp;&nbsp;&nbsp;&nbsp;<u>Possible System Failure</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Order entry systems have been designed to provide an efficient and
          dependable method for entering orders. Commercial internet service
          providers are not 100% reliable and a failure by one or more of these
          providers may affect internet based order entry. The Customer
          acknowledges that the order entry system is a mechanical system and as
          such may be subject to failure beyond the control of Kitco.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          16.3.&nbsp;&nbsp;&nbsp;&nbsp;<u>Internet Security</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco adheres to the highest security measures to ensure Customer data
          is protected against theft, loss, and corruption, and against the
          misuse and alteration of any Customer data stored on our servers.
          However, when a Customer accesses their account via a public or
          unsecured computer terminal or if a Customer chooses to share their
          account username and password (“
          <b>Electronic Identification Information</b>”), Kitco cannot guarantee
          the security of account data.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Sensitive communication between the Customer and Kitco’s online store
          is always protected via encryption while in transit, using the SSL
          encryption standard. However, the Customer should be aware that Kitco
          has no control over the privacy of Customer e-mail communications with
          us. We highly recommend that Customers not include private and
          sensitive information in e-mails to Kitco, including, but not limited
          to, account numbers, balances, passwords, Electronic Identification
          Information, etc. Kitco will not be held liable for any damages
          suffered by Customers should they transmit confidential or sensitive
          information to us through e-mail.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Information provided on Kitco’s website is not intended to provide
          legal, accounting, or tax advice, and should not be relied upon in
          that regard. Kitco is not responsible in any manner for direct,
          indirect, special or consequential damages howsoever caused, arising
          out of the use of its website.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          16.4.&nbsp;&nbsp;&nbsp;&nbsp;<u>Indemnification</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Except as otherwise expressly provided, Kitco shall not be liable for
          any loss or damage, whether direct or indirect, resulting from the
          transactions contemplated by these Terms and Conditions.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco shall not be liable under any circumstances whatsoever for
          special, incidental, consequential, indirect or punitive losses or
          damages (including lost profits or lost savings), whether or not
          caused by the fault or neglect of Kitco and whether or not Kitco had
          knowledge that such losses or damages might be incurred.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          The limitations and exclusions in this clause shall apply to all
          claims of whatsoever nature, kind and description, whether arising
          from breach of contract, delay or non-delivery of services, negligence
          or otherwise.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          16.5.&nbsp;&nbsp;&nbsp;&nbsp;<u>Force Majeure</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco shall not be liable for any failure to perform its obligations
          hereunder due to fire, computer viruses, network failure, computer
          hardware failure, explosion, flood, lightning, act of terrorism, war,
          rebellion, riot, sabotage, orders or requests of any government or any
          other authority, legislative changes, strikes, lockouts or other labor
          disputes, or events or circumstances beyond its reasonable control,
          but Kitco shall use commercially reasonable endeavors to minimize
          dangers or losses to the Customer as a consequence of such events.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          16.6.&nbsp;&nbsp;&nbsp;&nbsp;<u>Joint Accounts</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          For joint accounts with Kitco, Kitco is authorized to act on the
          instructions of either one of the named Customers, without further
          enquiry with regard to any and all transactions in any way related to
          the joint account and the disposition, transfer, deposit or withdrawal
          of any amount or of any or all of the funds or Kitco Pool held in the
          joint account. Kitco shall have no responsibility or obligation for
          further enquiry into such apparent authority, and shall bear no
          liability and disclaims all responsibility for the consequences of any
          acts or omissions made in reliance upon such instructions.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '6.0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          17. Customer Obligation and Liability
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          17.1.&nbsp;&nbsp;&nbsp;&nbsp;<u>Ownership and Authority</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Customer hereby warrants that it is either the owner or the authorized
          agent of the owner of any Precious Metal Bullion or Scrap Metal sent
          or brought to Kitco for the purpose of a Customer Sale Transaction or
          a Scrap Metal Transaction. Customer further warrants that it is
          authorized to accept and is accepting the terms of this Agreement not
          only for itself but also as agent for or on behalf of all other
          parties who have or may hereafter have any interest in said Precious
          Metal Bullion or Scrap Metal.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          17.2.&nbsp;&nbsp;&nbsp;&nbsp;<u>Compliance and Due Diligence</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          The Customer is responsible for complying with all laws of the
          jurisdiction from which the Customer accesses the Kitco Website or
          receives the services described herein, and the Customer shall at all
          times be solely responsible for obtaining any authorizations required
          by any authoritative body in such jurisdiction.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          The Customer has carried out reasonable due diligence to ensure that
          the purchase and sale of Precious Metals as provided under these Terms
          and Conditions is not contrary to any laws or regulations of the
          Customer’s governing jurisdiction, and that the acceptance of these
          Terms and Conditions by the Customer and the entering into of a
          Customer Purchase Transaction or Customer Sale Transaction or Scrap
          Metal Transaction is not contrary to any federal, provincial, state or
          any other law or regulation applicable to the Customer.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          17.3.&nbsp;&nbsp;&nbsp;&nbsp;
          <u>Protection of Electronic Identification Information</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          It is the Customer’s obligation to ensure that its Electronic
          Identification Information is kept secret. The Customer agrees to keep
          their Electronic Identification Information and all components thereof
          secret and safe to prevent unauthorized use.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          17.4.&nbsp;&nbsp;&nbsp;&nbsp;<u>Notification</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          If a Customer believes that any transaction or balance recorded in
          their account is incorrect, the Customer must contact Kitco
          immediately to notify Kitco of suspected unauthorized use of
          Customer’s Electronic Identification Information. Customers are
          responsible for ensuring the accuracy of the information displayed in
          their account, howsoever accessed. Kitco will not be held liable
          should a Customer fail to disclose any unauthorized use of Electronic
          Identification Information and Customer’s Kitco Accounts.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          17.5.&nbsp;&nbsp;&nbsp;&nbsp;<u>Customer Liability</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco will be under no obligation to confirm the actual identity or
          authority of any user of the Electronic Identification Information or
          any component thereof.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          The Customer will not be responsible for any unauthorized use of
          Electronic Identification Information occurring after notifying Kitco
          of the suspected unauthorized use as indicated in sub-section 15.4
          above.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          The Customer represents and warrants to Kitco that any funds or
          Precious Metals advanced, remitted or sold by the Customer to Kitco in
          connection with a <b>Customer Account, a Customer Sale Transaction</b>
          &nbsp;and/or a Customer Purchase Transaction will not represent
          proceeds of crime for the purposes of the PCMLA Proceeds of Crime
          (Money Laundering) and Terrorist Financing Act (Canada) (the “
          <b>PCMLTFA</b>
          ”). The Customer further represents and warrants to Kitco that none of
          the funds provided by the Customer to Kitco: (i) have been or will be
          derived from or related to any activity that is deemed criminal under
          the laws of Canada, the United States, or any other jurisdiction; or
          (ii) are being tendered on behalf of a person or entity other than the
          Customer, and the Subscriber will promptly notify Kitco if the
          Customer discovers that any of such representations cease to be true
          and provide Kitco with appropriate information in connection
          therewith;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '6.0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          18. Intellectual Property, Trademarks, Logos
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal, styles.ml20)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          All of the intellectual property rights including without limitation
          trademarks, service marks, trade names, copyright and other rights
          used or embodied in this website are and will remain the sole property
          of Kitco (or its suppliers where applicable).
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal, styles.ml20)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          All information and material supplied by Kitco, constitutes part of
          Kitco&apos;s confidential and proprietary information and no Customer
          participating in any of its programs may reproduce, copy or disclose
          such information without the prior written consent of Kitco.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal, styles.ml20)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          No party accessing this website may attempt to tamper with, modify,
          reverse engineer, gain unauthorized access to, or in any way alter any
          software used in Kitco’s programs or comprising Kitco’s website.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '6.0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          19. Maintaining an Active Kitco Account
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '10.0pt',
          marginLeft: '17.0pt',
          display: 'flex',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: 'Symbol',
            color: '#7F7F7F',
          }}
        >
          <span>19.1.</span>
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco reserves the right to deem the Customer’s Kitco Account as
          inactive if there exists no account activity in the account for a
          period of eighteen (18) consecutive months.
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        />
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '10.0pt',
          marginLeft: '17.0pt',
          display: 'flex',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: 'Symbol',
            color: '#7F7F7F',
          }}
        >
          <span>19.2.</span>
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Where the Customer&apos;s Kitco Account has been deemed inactive in
          accordance with section 19.1, Kitco may, at its discretion, deactivate
          the Customer&apos;s Kitco Account following sixty (60) calendar days
          prior written notice to the Customer.
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        />
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '10.0pt',
          marginLeft: '17.0pt',
          display: 'flex',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: 'Symbol',
            color: '#7F7F7F',
          }}
        >
          <span>19.3.</span>
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          The Customer’s Kitco Account will be assessed individually for
          activity; as such, the deactivation of one Kitco Account shall be
          considered separately from any other existing Kitco Account.
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        />
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '10.0pt',
          marginLeft: '17.0pt',
          display: 'flex',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: 'Symbol',
            color: '#7F7F7F',
          }}
        >
          <span>19.4.</span>
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco may also, at its discretion, deactivate the Customer’s Kitco
          Account by giving the Customer a written notice to that effect upon or
          following the termination of the Agreement in accordance with Section
          13.2 of this Agreement.
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        />
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '10.0pt',
          marginLeft: '17.0pt',
          display: 'flex',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: 'Symbol',
            color: '#7F7F7F',
          }}
        >
          <span>19.5.</span>
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          If, upon the Kitco Account being deactivated, there exists a positive
          balance therein, the Customer may make arrangements to transfer
          balance of cleared funds, stored Precious Metal Products, Kitco Pool
          and Kitco VaultChain<sup>TM</sup>, the Customer may transfer the
          positive balance to another Kitco Account.
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        />
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '10.0pt',
          marginLeft: '17.0pt',
          display: 'flex',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: 'Symbol',
            color: '#7F7F7F',
          }}
        >
          <span>19.6.</span>
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Where the Customer makes a transfer of the positive balance, the
          transaction will be subject to all applicable processing and
          administrative fees.
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        />
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '10.0pt',
          marginLeft: '17.0pt',
          display: 'flex',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: 'Symbol',
            color: '#7F7F7F',
          }}
        >
          <span>19.7.</span>
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Where there is a positive balance in the Customer&apos;s Kitco Account
          being deactivated and the Customer fails to make a transfer of the
          remaining balance of cleared funds, stored Precious Metal Products,
          Kitco Pool and Kitco VaultChain<sup>TM</sup> within five (5) years
          from the date the Customer’s Kitco Account has been deactivated, the
          Customer&apos;s Kitco Account will be deemed to be abandoned, and will
          be closed. Any balance in the Customer&apos;s Kitco Account will be
          removed and held by Kitco in reserve for a further five (5) years. The
          remaining balance may still be claimed by the Customer during this
          5-year period, after which time any remaining balance (including
          cleared funds, stored Precious Metal Products, Kitco Pool and Kitco
          VaultChain<sup>TM</sup>) will be forfeited and paid to Kitco.
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        />
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '10.0pt',
          marginLeft: '17.0pt',
          display: 'flex',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: 'Symbol',
            color: '#7F7F7F',
          }}
        >
          <span>19.8.</span>
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          If, upon deactivation, there exists a negative balance in the
          Customer&apos;s Kitco Account, the Customer or its successors and
          assigns shall be required to settle any such negative balance with
          Kitco.
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        />
      </p>

      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '6.0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          20. Miscellaneous
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          20.1.&nbsp;&nbsp;&nbsp;&nbsp;<u>Insolvency</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          In the event: (i) that a receiver or trustee is appointed in respect
          of the property and assets of the Customer as a result of the latter’s
          insolvency or bankruptcy, and (ii) that Kitco is requested by the
          receiver or trustee to remit any Customer holdings, the receiver or
          trustee will direct any Customer holding liquidation requests to
          Kitco. Any inquiries by the said trustee or receiver regarding the
          present Agreement between the Customer and Kitco shall be directed to
          Kitco.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          20.2.&nbsp;&nbsp;&nbsp;&nbsp;<u>Death of Customer</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '40.0pt',
          }}
        >
          20.2.1&nbsp;&nbsp;&nbsp;&nbsp;<u>Individual Account</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          In the event of Customer death, Kitco will require full documentation
          of the Customer’s estate, including a copy of a death certificate,
          will or trust documentation, and any probate information if
          applicable. Upon confirmation of a Customer death, Kitco will freeze
          the Customer’s account and act in accordance with the conditions set
          out in the Customer’s will, or as instructed by the trustee of the
          Customer’s estate. It is the sole responsibility of the Customer to
          ensure that such documentation will be provided for in the event of
          death.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '40.0pt',
          }}
        >
          20.2.2&nbsp;&nbsp;&nbsp;&nbsp;<u>Joint Account</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Except for accounts where either of the Customers in a joint account
          with Kitco resides in the Province of Quebec, in the event of death of
          any one of the named Customers, the deceased Customer’s interest in
          the Account will pass automatically to the surviving Customer.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          It is agreed that in the event of death of one of the named Customers,
          the surviving Customer shall immediately give Kitco written notice
          thereof, and Kitco may require such papers, retain such portion and/or
          take any measures it deems advisable, including restricting
          transactions or liquidating the joint account, to protect itself
          against any tax, liability, penalty or loss under any applicable laws.
          The estate of the deceased Customer and the surviving Customer shall
          continue to be liable, jointly and severally, to Kitco for any
          obligation incurred prior to Kitco’s receipt of written notice of the
          death of such Customer or for any loss, damage or costs incurred by
          Kitco, including reasonable attorney fees, in any dispute between the
          estate of a deceased Customer, the surviving Customer or a third
          party.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          20.3.&nbsp;&nbsp;&nbsp;&nbsp;<u>Changes to Terms and Conditions</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Kitco reserves the right to amend these Terms and Conditions, any
          applicable fees and charges, or any services, rights or obligations
          provided for herein at its sole discretion, at any time, without prior
          notice to the Customer. Acceptance of these Terms and Conditions is
          limited to the acknowledged transaction and is not applicable to any
          future transactions. The Customer will be responsible for accepting
          the Terms and Conditions for every subsequent transaction.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          20.4.&nbsp;&nbsp;&nbsp;&nbsp;<u>Applicable Law</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          This Agreement shall be construed in accordance with and governed by
          the laws applicable in the Province of Quebec and the laws of Canada
          applicable therein. The Parties hereby irrevocably submit to the non-
          exclusive jurisdiction of the Courts of the province of Quebec in
          respect of all matters or disputes arising from the Agreement, except
          as otherwise specifically stated herein.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          20.5.&nbsp;&nbsp;&nbsp;&nbsp;<u>Binding Effect</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          This Agreement is binding upon the parties hereto and their respective
          successors, heirs, legal representative and permitted assigns.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          20.6.&nbsp;&nbsp;&nbsp;&nbsp;<u>Severability</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          In the event that any provision in this Agreement is determined to be
          invalid, illegal, or unenforceable, such determination shall not
          affect the validity and enforceability of any other remaining
          provisions of this Agreement.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          20.7.&nbsp;&nbsp;&nbsp;&nbsp;<u>Language</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          The parties hereto have expressly required that this agreement and all
          deeds, documents or notices relating thereto be executed in the
          English language.{' '}
          <i>
            Les parties aux présentes ont expressément convenu que cette entente
            et tout autre acte, document ou avis y afférent soient rédigés en
            anglais.
          </i>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          20.8.&nbsp;&nbsp;&nbsp;&nbsp;<u>Privacy</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
            float: 'left',
          }}
        >
          Kitco respects your privacy.&nbsp;&nbsp;Our privacy policy is
          available at:&nbsp;
        </span>
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <a href="https://online.kitco.com/help/privacy_policy.html">
            <span
              style={{
                fontSize: '12.0pt',
                fontFamily: '"Mulish",serif',
                color: '#95B3D7',
                textDecoration: 'underline',
                wordBreak: 'break-word',
              }}
            >
              https://online.kitco.com/help/privacy_policy.html
            </span>
          </a>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '6.0pt',
          textAlign: 'center',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          Schedule of Transaction Restrictions
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          1. Customer Purchase Transaction Restrictions
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal, styles.ml20)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '14.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>
            Customer Purchase Transactions submitted by Customers residing or
            having their principal place of business{' '}
            <u>in either Canada or the United States</u>
          </b>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal, styles.ml20)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Customers who have Open Transactions with a total value at the sizes
          identified in the table below (USD for U.S. dollar transactions, in
          CAD for Canadian dollar transactions) are required to provide the
          relevant collateral in order to confirm a Customer Purchase
          Transaction. The Customer acknowledges that providing a valid credit
          card does not constitute payment, and confirms they will abide by the
          payment terms as stated in Section 4.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>

      <table className={styles.table}>
        <thead>
          <tr>
            <th className="w-[30%] md:w-[48%]">Open Transaction (Size)</th>
            <th className="w-[30%] md:w-[24%]">
              Collateral Deposit Requirement
              <p>(As % of Open Transaction Total)</p>
            </th>
            <th className="w-[40%] md:w-[28%]">Required Forms of Collatera</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Up to and including $50,000</td>
            <td />
            <td>Valid Visa or Mastercard</td>
          </tr>
          <tr>
            <td>Greater than $50,000 and up to $100,000</td>
            <td>15%</td>
            <td>
              Valid Visa or Mastercard
              <p>+</p>
              Cleared Funds with Kitco, or Cleared Kitco Pool, or Cleared Kitco
              VaultChain<sup>TM</sup>
            </td>
          </tr>
          <tr>
            <td>Greater than $100,000</td>
            <td>25%</td>
            <td>
              Valid Visa or Mastercard
              <p>+</p>
              Cleared Funds with Kitco, or Cleared Kitco Pool, or Cleared Kitco
              VaultChain<sup>TM</sup>
            </td>
          </tr>
        </tbody>
      </table>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>

      <p
        className={clsx(styles.MsoNormal, styles.ml20)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '14.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>
            Customer Purchase Transactions submitted by Customers residing or
            having their principal place of business{' '}
            <u>outside of Canada and the United States</u>
          </b>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal, styles.ml20)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Customers who have Open Transactions with a total value at the sizes
          identified in the table below (USD for U.S. dollar transactions and in
          USD equivalents for transactions in all other currencies, rounded up
          to the nearest $1000) are required to provide relevant collateral in
          order to confirm a transaction purchase price. The Customer
          acknowledges that providing a valid credit card does not constitute
          payment, and confirms they will abide by the payment terms as stated
          in Section 4.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <table className={styles.table}>
        <thead>
          <tr>
            <th className="w-[30%] md:w-[48%]">Open Transaction (Size)</th>
            <th className="w-[30%] md:w-[24%]">
              Collateral Deposit Requirement
              <p>(As % of Open Transaction Total)</p>
            </th>
            <th className="w-[40%] md:w-[28%]">Required Forms of Collatera</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Up to and including $20,000</td>
            <td />
            <td>Valid Visa or Mastercard</td>
          </tr>
          <tr>
            <td>Greater than $20,000 and up to $100,000</td>
            <td>15%</td>
            <td>
              Valid Visa or Mastercard
              <p>+</p>
              Cleared Funds with Kitco, or Cleared Kitco Pool, or Cleared Kitco
              VaultChain<sup>TM</sup>
            </td>
          </tr>
          <tr>
            <td>Greater than $100,000</td>
            <td>25%</td>
            <td>
              Valid Visa or Mastercard
              <p>+</p>
              Cleared Funds with Kitco, or Cleared Kitco Pool, or Cleared Kitco
              VaultChain<sup>TM</sup>
            </td>
          </tr>
        </tbody>
      </table>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          2. Customer Sale Transaction Restrictions
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal, styles.ml20)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '14.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>
            Customer Sale Transactions submitted by Customers residing or having
            their principal place of business{' '}
            <u>in either Canada or the United States</u>
          </b>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal, styles.ml20)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Customers who have Open Transactions with a total value at the sizes
          identified in the table below (USD for U.S. dollar transactions, in
          CAD for Canadian dollar transactions) are required to provide the
          relevant collateral in order to confirm a Customer Sale Transaction.
          The Customer acknowledges that by providing a valid credit card they
          will abide by the payment terms as stated in Section 4.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <table className={styles.table}>
        <thead>
          <tr>
            <th className="w-[30%] md:w-[48%]">Open Transaction (Size)</th>
            <th className="w-[30%] md:w-[24%]">
              Collateral Deposit Requirement
              <p>(As % of Open Transaction Total)</p>
            </th>
            <th className="w-[40%] md:w-[28%]">Required Forms of Collatera</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Up to and including $50,000</td>
            <td />
            <td>Valid Visa or Mastercard</td>
          </tr>
          <tr>
            <td>Greater than $50,000 and up to $100,000</td>
            <td>15%</td>
            <td>
              Valid Visa or Mastercard
              <p>+</p>
              Cleared Funds with Kitco, or Cleared Kitco Pool, or Cleared Kitco
              VaultChain<sup>TM</sup>
            </td>
          </tr>
          <tr>
            <td>Greater than $100,000</td>
            <td>25%</td>
            <td>
              Valid Visa or Mastercard
              <p>+</p>
              Cleared Funds with Kitco, or Cleared Kitco Pool, or Cleared Kitco
              VaultChain<sup>TM</sup>
            </td>
          </tr>
        </tbody>
      </table>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal, styles.ml20)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '14.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          <b>
            Customer Sale Transactions submitted by Customers residing or having
            their principal place of business{' '}
            <u>outside of Canada and the United States</u>
          </b>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal, styles.ml20)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Customers who have Open Transactions with a total value at the sizes
          identified in the table below (USD for U.S. dollar transactions and in
          USD equivalents for transactions in all other currencies, rounded up
          to the nearest $1000) are required to provide sufficient collateral in
          order to confirm a transaction sale price. The Customer acknowledges
          that by providing a valid credit card they will abide by the payment
          terms as stated in Section 4.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <table className={styles.table}>
        <thead>
          <tr>
            <th className="w-[30%] md:w-[48%]">Open Transaction (Size)</th>
            <th className="w-[30%] md:w-[24%]">
              Collateral Deposit Requirement
              <p>(As % of Open Transaction Total)</p>
            </th>
            <th className="w-[40%] md:w-[28%]">Required Forms of Collatera</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Up to and including $20,000 </td>
            <td />
            <td>Valid Visa or Mastercard</td>
          </tr>
          <tr>
            <td>Greater than $20,000 and up to $100,000</td>
            <td>15%</td>
            <td>
              Valid Visa or Mastercard
              <p>+</p>
              Cleared Funds with Kitco, or Cleared Kitco Pool, or Cleared Kitco
              VaultChain<sup>TM</sup>
            </td>
          </tr>
          <tr>
            <td>Greater than $100,000</td>
            <td>25%</td>
            <td>
              Valid Visa or Mastercard
              <p>+</p>
              Cleared Funds with Kitco, or Cleared Kitco Pool, or Cleared Kitco
              VaultChain<sup>TM</sup>
            </td>
          </tr>
        </tbody>
      </table>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          3. Scrap Metal Transaction Restrictions
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal, styles.ml20)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Customers who have Open Transactions with a total value at the sizes
          identified in the table below (USD for U.S. dollar transactions, in
          CAD for Canadian dollar transactions) are required to provide
          sufficient collateral in order to confirm a Scrap Metal Transaction.
          The Customer acknowledges that by providing a valid credit card they
          will abide by the payment terms as stated in Section 4.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <table className={styles.table}>
        <thead>
          <tr>
            <th className="w-[30%] md:w-[48%]">Open Transaction (Size)</th>
            <th className="w-[30%] md:w-[24%]">
              Collateral Deposit Requirement
              <p>(As % of Open Transaction Total)</p>
            </th>
            <th className="w-[40%] md:w-[28%]">Required Forms of Collatera</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Up to and including $50,000 </td>
            <td />
            <td>Valid Visa or Mastercard</td>
          </tr>
          <tr>
            <td>Greater than $50,000</td>
            <td>15%</td>
            <td>
              Valid Visa or Mastercard
              <p>+</p>
              Cleared Funds with Kitco, or Cleared Kitco Pool, or Cleared Kitco
              VaultChain<sup>TM</sup>
            </td>
          </tr>
        </tbody>
      </table>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '6.0pt',
          textAlign: 'center',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          Schedule of Accepted Payment Methods
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          Kitco accepts the following as methods of payment for transactions or
          for adding funds to a Kitco Account:
        </span>
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          1. Customers residing or having their principal place of business{' '}
          <u>in either Canada or the United States</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal, styles.ml20)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Customers residing or having their principal place of business in
          Canada or the United States may use the following methods to pay for a
          Customer Purchase Transaction or to add funds to their Kitco Account,
          at the minimum holding periods defined below:
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <table className={styles.table}>
        <thead>
          <tr>
            <th className="w-[28%] pl-2 pr-2 md:pr-5">Payment Type</th>
            <th className="w-[32%]">Holding Period</th>
            <th className="w-[20%]">Payment Limits – EXISTING CUSTOMERS</th>
            <th className="w-[20%]">Payment Limits – NEW CUSTOMERS</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td className="pl-2 pr-2 md:pr-5">
              <p>Cleared FOA</p>
              <p>Bank Wire</p>
              <p>Online Bill Pay -</p>
            </td>
            <td>No Holding Period</td>
            <td>No Limit</td>
            <td>No Limit</td>
          </tr>
          <tr>
            <td className="pl-2 pr-2 md:pr-5">
              <p>Cashier’s Check</p>
              <p>Bank Draft</p>
              <p>Bank Money Order</p>
            </td>
            <td>3 Business Days</td>
            <td>$50,000</td>
            <td>$25,000</td>
          </tr>
          <tr>
            <td className="pl-2 pr-2 md:pr-5">Personal / Company Check</td>
            <td>5 Business Days</td>
            <td>$50,000</td>
            <td>$25,000</td>
          </tr>
          <tr>
            <td className="pl-2 pr-2 md:pr-5">
              E-Check (ACH) (United States Only, USD)
            </td>
            <td>3 Business Days</td>
            <td>$50,000</td>
            <td>$25,000</td>
          </tr>
          <tr>
            <td className="pl-2 pr-2 md:pr-5">Credit Card/PayPal</td>
            <td>No Holding Period</td>
            <td>$25,000 per transaction</td>
            <td>$10,000 per 14-day period</td>
          </tr>
        </tbody>
      </table>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal, styles.ml20)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Customers who wish to add funds to their Kitco Account in either Euros
          or British Pounds may only do so by bank wire.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          2. Customers residing or having their principal place of business{' '}
          <u>outside of Canada and the United States</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal, styles.ml20)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Customers residing or having their principal place of business outside
          of Canada and the United States may add funds to their Kitco Account
          by bank wire only and may pay for a Customer Purchase Transaction by
          bank wire or using cleared funds on their Kitco Account.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          3. Settlement options and fees on Proceeds from Customer Sale
          Transactions, Scrap Metal Transactions and Return of Funds
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal, styles.ml20)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Settlement options, along with the corresponding fees and transaction
          limits, for Customer Sale Transactions, Scrap Metal Transactions and
          Return of Funds are as follows:
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <table className={cs([styles.table, styles.tableLarg])}>
        <thead>
          <tr>
            <th className="w-[25%] !text-center md:w-[30%]">Settlement Type</th>
            <th className="w-[60%] md:w-[50%]">
              <div className="flex flex-col">
                <div className="border-b py-2">
                  <p className="mx-auto max-w-[290px] !font-bold">
                    Customer Residence or Principal Place of Business
                  </p>
                </div>
                <div className="flex">
                  <div className="flex flex-1 items-center justify-center border-r">
                    Canada
                  </div>
                  <div className="flex flex-1 items-center justify-center border-r">
                    USA
                  </div>
                  <div className="flex flex-1 items-center justify-center">
                    All Other Countries
                  </div>
                </div>
              </div>
            </th>
            <th className="w-[15%] md:w-[20%]">Maximum Transaction Limit</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td className="p-2 !text-center">
              Check by regular mail (USD or CAD transactions only)
            </td>
            <td>
              <div className="flex">
                <div className="flex flex-1 items-center justify-center border-r">
                  No Fees
                </div>
                <div className="flex flex-1 items-center justify-center border-r">
                  No Fees
                </div>
                <div className="flex flex-1 items-center justify-center">
                  Not Available
                </div>
              </div>
            </td>
            <td>No Limit</td>
          </tr>
          <tr>
            <td className="p-2 !text-center">
              Check by courier (USD or CAD transactions only)
            </td>
            <td>
              <div className="flex">
                <div className="flex flex-1 items-center justify-center border-r">
                  CAD 30
                </div>
                <div className="flex flex-1 items-center justify-center border-r">
                  USD 30
                </div>
                <div className="flex flex-1 items-center justify-center">
                  Not Available
                </div>
              </div>
            </td>
            <td>No Limit</td>
          </tr>
          <tr>
            <td className="p-2 !text-center">Bank Wire on CAD transactions</td>
            <td>
              <div className="flex">
                <div className="flex flex-1 items-center justify-center border-r">
                  CAD 40
                </div>
                <div className="flex flex-1 items-center justify-center border-r">
                  CAD 60
                </div>
                <div className="flex flex-1 items-center justify-center">
                  CAD 60
                </div>
              </div>
            </td>
            <td>No Limit</td>
          </tr>
          <tr>
            <td className="p-2 !text-center">Bank Wire on USD transactions</td>
            <td>
              <div className="flex">
                <div className="flex flex-1 items-center justify-center border-r">
                  USD 60
                </div>
                <div className="flex flex-1 items-center justify-center border-r">
                  USD 40
                </div>
                <div className="flex flex-1 items-center justify-center">
                  USD 60
                </div>
              </div>
            </td>
            <td>No Limit</td>
          </tr>
          <tr>
            <td className="p-2 !text-center">Bank Wire on GBP transactions</td>
            <td>GBP 50</td>
            <td>No Limit</td>
          </tr>
          <tr>
            <td className="p-2 !text-center">Bank Wire on EUR transactions</td>
            <td>EUR 60</td>
            <td>No Limit</td>
          </tr>
          <tr>
            <td className="p-2 !text-center">
              Deposit as funds on Customer’s account (all currencies)
            </td>
            <td>No Fees</td>
            <td>No Limit</td>
          </tr>
          <tr>
            <td className="p-2 !text-center">
              Conversion to Kitco Pool or VaultChain<sup>TM</sup>
            </td>
            <td>No Fees</td>
            <td>No Limit</td>
          </tr>
          <tr>
            <td className="p-2 !text-center">
              Precious Metal Bullion products normally carried by Kitco
            </td>
            <td>
              Fabrication, shipping & insurance fees may apply depending on
              product
            </td>
            <td>No Limit</td>
          </tr>
        </tbody>
      </table>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '6.0pt',
          textAlign: 'center',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          Schedule of Fees
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          This schedule outlines fees that Kitco may charge the Customer in
          relation to Precious Metal Bullion, Kitco Pool products, Kitco
          VaultChain<sup>TM</sup> products, Scrap Metal Transactions, and
          related services. Such fees, which are subject to change from time to
          time at the discretion of Kitco, are also posted on the Kitco Online
          Store. Unless otherwise specified, all fees stated are in both US and
          Canadian Dollars. Fees related to a transaction placed in British
          Pounds or Euros will be converted based on the US Dollar exchange rate
          at the time the transaction is submitted.
        </span>
      </p>
      {/* header section */}
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          1. Fees for Transactions Involving Kitco Pool
        </span>
      </p>
      {/* END header section */}
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          Kitco Pool transactions are subject to the following applicable fees.
          These fees also apply in transactions in which Kitco Pool is purchased
          or sold in combination with Precious Metal Bullion.
        </span>
      </p>
      <div className="mx-auto md:w-[80%]">
        <table className={styles.table}>
          <thead>
            <tr>
              <th className="w-[55%] py-1 pl-2 pr-2 md:w-[70%]">Transaction</th>
              <th className="w-[45%] py-1 md:w-[30%]">Fee Amount</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="py-1 pl-2 pr-2 md:pr-5">
                Customer Purchase Transactions submitted by telephone or in
                person
              </td>
              <td>
                $14.99
                <p style={{ fontSize: '9.0pt' }}>
                  inclusive of applicable taxes
                </p>
              </td>
            </tr>
            <tr>
              <td className="py-1 pl-2 pr-2 md:pr-5">
                Customer Sale Transactions submitted by telephone or in person
              </td>
              <td>
                $14.99
                <p style={{ fontSize: '9.0pt' }}>
                  inclusive of applicable taxes
                </p>
              </td>
            </tr>
            <tr>
              <td className="py-1 pl-2 pr-2">
                Customer Purchase Transactions submitted online
              </td>
              <td>
                $8.98
                <p style={{ fontSize: '9.0pt' }}>
                  inclusive of applicable taxes
                </p>
              </td>
            </tr>
            <tr>
              <td className="py-1 pl-2 pr-2">
                Customer Sale Transactions submitted online
              </td>
              <td>
                $8.98
                <p style={{ fontSize: '9.0pt' }}>
                  inclusive of applicable taxes
                </p>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      {/* header section */}
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          2. Fees for Transactions and Holdings Involving Kitco VaultChain
          <sup>TM</sup>
        </span>
      </p>
      {/* END header section */}
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          Kitco VaultChain<sup>TM</sup> accounts and transactions are subject to
          applicable fees as follows:
        </span>
      </p>
      <div className="mx-auto md:w-[80%]">
        <table className={styles.table}>
          <thead>
            <tr>
              <th className="w-[55%] py-1 pl-2 pr-2 md:w-[70%]">
                Service or Transaction
              </th>
              <th className="w-[45%] py-1 md:w-[30%]">Fee Amount</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="py-1 pl-2 pr-2">
                VaultChain<sup>TM</sup> Account Opening
              </td>
              <td>No Fee</td>
            </tr>
            <tr>
              <td className="py-1 pl-2 pr-2">
                VaultChain<sup>TM</sup> Administrative Fee
              </td>
              <td>No Fee</td>
            </tr>
            <tr>
              <td className="py-1 pl-2 pr-2">
                VaultChain<sup>TM</sup> Account Annual Fee*
              </td>
              <td>$100 annually</td>
            </tr>
            <tr>
              <td className="py-1 pl-2 pr-2">
                VaultChain<sup>TM</sup> Storage Fee Rates**
              </td>
              <td>
                VaultChain<sup>TM</sup> Gold: 0.12% annually
                <br />
                VaultChain<sup>TM</sup> Silver: 0.30% annually
              </td>
            </tr>
            <tr>
              <td className="py-1 pl-2 pr-2">
                VaultChain<sup>TM</sup> Transaction Fee
              </td>
              <td>variable fee – confirmed with each transaction</td>
            </tr>
          </tbody>
        </table>
      </div>
      {/* space for a line break */}
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      {/* END space for a line break */}

      {/* body section */}
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          * Kitco VaultChain<sup>TM</sup> account annual fees will be pro-rated
          on a quarterly basis based on the number of quarters having elapsed in
          a given calendar year at the time of the initial Kitco VaultChain™
          purchase and will be charged at a rate of $100 annually thereafter.
        </span>
      </p>
      {/* END body section */}
      {/* body section */}
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          ** VaultChain<sup>TM</sup> Storage Fees will be charged to Customers
          annually, in arrears. These fees are calculated daily for all precious
          metals held in the VaultChain<sup>TM</sup> account by using the most
          recent price fix published by the London Bullion Market Association
          for gold and silver. This price is multiplied by the number of fine
          troy ounces of each metal type in the account, then by the storage fee
          rate, and divided by 365 (or 366 in a leap year) to determine the
          daily storage fee. The daily storage fees for each metal type are
          added together, and any VaultChain<sup>TM</sup> Account Annual Fee
          paid for the same year are subtracted. The remaining balance is then
          charged to Customers annually.
        </span>
      </p>
      {/* END body section */}
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
      {/* header section */}
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          3. Fees Applicable to Customer Sale Transactions and Scrap Metal
          Transactions
        </span>
      </p>
      {/* END header section */}
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          3.1.&nbsp;&nbsp;&nbsp;&nbsp;<u>Melt & Assay Fees</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal, styles.ml45)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          Fees are variable when a melt and/or assay are required, depending on
          the Precious Metals Bullion Products sold by the Customer to Kitco.
          However, if a Kitco representative can test Customer’s Scrap Metal
          with a scratch test, there will be no processing fees.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '6.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
            marginLeft: '17.0pt',
          }}
        >
          3.2.&nbsp;&nbsp;&nbsp;&nbsp;
          <u>Handling Fees for Testing Scrap Metal</u>
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal, styles.ml45)}
        style={{
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
          marginBottom: '0pt',
        }}
      >
        <span
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          A handling fee of $70 (in USD for U.S. dollar transactions, in CAD for
          Canadian dollar transactions and in USD equivalents for transactions
          in all other currencies) applies. Said handling fee shall apply should
          Customer send Scrap Metal to Kitco for testing without completing a
          Scrap Metal Transaction.
        </span>
      </p>
      <p
        className={clsx(styles.MsoNormal)}
        style={{
          marginBottom: '0pt',
          textAlign: 'justify',
          textJustify: 'inter-word',
          lineHeight: '16.0pt',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#7F7F7F',
          }}
        >
          &nbsp;
        </span>
      </p>
      {/* header section */}
      <p
        className={clsx(styles.MsoListParagraph)}
        style={{
          marginTop: '0pt',
          marginRight: '0cm',
          marginBottom: '12.0pt',
          marginLeft: '0cm',
          lineHeight: 'normal',
        }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '16.0pt',
            fontFamily: '"Lato",sans-serif',
            color: '#373737',
          }}
        >
          4. Other Fees Related to Customer Transactions
        </span>
      </p>
      {/* END header section */}
      <div className="mx-auto md:w-[95%]">
        <table className={styles.table}>
          <thead>
            <tr>
              <th className="w-[55%] py-1 pl-2 pr-2 !text-center md:w-[70%]">
                Fee or Service{' '}
              </th>
              <th className="w-[30%] w-[45%] py-1">Fee Amount</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="px-2 py-1">Fees on Returned Checks</td>
              <td className="px-2">$35 inclusive of applicable taxes</td>
            </tr>
            <tr>
              <td className="px-2 py-1">Fees on Transaction Cancellations</td>
              <td className="px-2">
                Market Loss + $50 inclusive of applicable taxes
              </td>
            </tr>
            <tr>
              <td className="px-2 py-1">Changes to Delivery Address</td>
              <td className="px-2">$20 inclusive of applicable taxes</td>
            </tr>
            <tr>
              <td className="px-2 py-1">Undeliverable Packages</td>
              <td className="px-2">$20 inclusive of applicable taxes</td>
            </tr>
            <tr>
              <td className="px-2 py-1">Shipping and Insurance Fees</td>
              <td className="px-2">
                Variable fee – confirmed with each transaction
              </td>
            </tr>
            <tr>
              <td className="px-2 py-1">Pick-up Fees</td>
              <td className="px-2">
                $70 minimum, varies depending on location
              </td>
            </tr>
            <tr>
              <td className="px-2 py-1">Drop-off Fees</td>
              <td className="px-2">
                $70 minimum, varies depending on location
              </td>
            </tr>
            <tr>
              <td className="px-2 py-1">Fabrication Fees</td>
              <td className="px-2">
                Variable fee – confirmed with each transaction
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <p
        className={clsx(styles.MsoNormal)}
        style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
      >
        <span
          lang="EN-US"
          style={{
            fontSize: '12.0pt',
            fontFamily: '"Mulish",serif',
            color: '#373737',
          }}
        >
          &nbsp;
        </span>
      </p>
    </div>
  </LayoutNoTopAdvertisement>
)

export default TermsAndConditions
