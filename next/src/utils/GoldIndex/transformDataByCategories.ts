import commodityCategories from '~/src/data/GoldIndex/CommodityCategories'
import type CommodityData from '~/src/types/DataTable/CommodityData'
import type { CategoryHeaderData, CategorySubHeaderData } from '~/src/types/DataTable/CommodityData'

/**
 * Transform commodity data to include category headers
 * @param data - Array of commodity data
 * @returns Array with category headers and commodity data organized by categories
 */
export const transformDataByCategories = (data: CommodityData[]): CommodityData[] => {
  const result: CommodityData[] = []
  
  // Define the order of categories
  const categoryOrder = [
    'PRECIOUS METALS',
    'CRYPTOCURRENCIES', 
    'BASE METALS',
    'ENERGY'
  ]

  // Create default change data for category headers
  const defaultChangeData = {
    change: '0',
    changeVal: 0,
    percentage: '0',
    percentageVal: 0,
  }

  // Create default bid data for category headers
  const defaultBidData = {
    bid: '0',
    bidVal: 0,
    currency: 'USD',
    originalTime: new Date().toISOString(),
  }

  categoryOrder.forEach((categoryKey) => {
    const categoryCommodities = commodityCategories[categoryKey]
    
    if (!categoryCommodities) return

    // Filter data for this category
    const categoryData = data.filter((item) => 
      categoryCommodities.includes(item.commodity)
    )

    // Only add category if it has data
    if (categoryData.length > 0) {
      // Add category header
      const categoryHeader: CategoryHeaderData = {
        commodity: `category-${categoryKey}`,
        type: 'categoryHeader',
        categoryName: categoryKey,
        categoryKey: categoryKey,
        lastBid: defaultBidData,
        changeDueToUSD: defaultChangeData,
        changeDueToTrade: defaultChangeData,
        totalChange: defaultChangeData,
      }

      result.push(categoryHeader)

      // Add category sub-header (column headers)
      const categorySubHeader: CategorySubHeaderData = {
        commodity: `category-subheader-${categoryKey}`,
        type: 'categorySubHeader',
        categoryName: categoryKey,
        categoryKey: categoryKey,
        lastBid: defaultBidData,
        changeDueToUSD: defaultChangeData,
        changeDueToTrade: defaultChangeData,
        totalChange: defaultChangeData,
      }

      result.push(categorySubHeader)

      // Add category data
      result.push(...categoryData)
    }
  })

  return result
}
