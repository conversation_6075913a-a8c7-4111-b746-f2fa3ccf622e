import type { FC } from 'react'
import type { BarchartQuote } from '~/src/generated'
import Items from './Items'

type RegionListProps = {
  quotes: Partial<BarchartQuote>[]
}

const RegionList: FC<RegionListProps> = ({ quotes }) => {
  return (
    <>
      {quotes?.map((v, idx: number) => (
        <Items
          v={v}
          key={v.name}
          itemClass={`${idx % 2 ? 'bg-[#f5f5f5]' : 'bg-[#fff]'}`}
        />
      ))}
    </>
  )
}

export default RegionList
