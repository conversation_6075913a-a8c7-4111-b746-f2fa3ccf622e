import { clsx } from 'clsx'
import * as Teaser from '~/src/components-news/Teasers/Teasers'
import type { ArticleTeaserFragmentFragment } from '~/src/generated'

export const FeaturedNext: React.FC<{
  data: ArticleTeaserFragmentFragment
}> = ({ data }) => (
  <Teaser.CtxProvider
    node={data}
    className={clsx(
      'flex gap-2.5 md:block',
      'last:border-none md:border-r md:border-[#E5E5E5] md:pr-5',
    )}
  >
    <Teaser.TImage
      width={200}
      height={100}
      className="block w-[120px] min-w-[120px] md:hidden"
      imageClassName="!rounded"
    />
    <div className="flex flex-col">
      <Teaser.Category />
      <Teaser.TitleLink className="pt-1 text-[16px] leading-[130%] md:mb-2" />
      <Teaser.Summary className="hidden md:line-clamp-3 " />
      <Teaser.DateTime className="pt-[0.375rem] md:pt-2" />
    </div>
  </Teaser.CtxProvider>
)
