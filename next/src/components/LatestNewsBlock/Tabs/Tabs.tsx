import type { FC } from 'react'
import cs from '~/src/utils/cs'
import styles from './Tabs.module.scss'

interface Props {
  currentTab: any
  setCurrentTab: any
  listTab: Array<string>
  // fetchMarketNews: () => void
  // fetchStreetNews: () => void
}

const Tabs: FC<Props> = ({
  currentTab,
  setCurrentTab,
  listTab,
  // fetchMarketNews,
  // fetchStreetNews,
}) => (
  <ul className={styles.tabs}>
    {listTab?.map((tab, index) => {
      return (
        <li
          key={index}
          className={cs([styles.tab, currentTab === tab && styles.activeTab])}
          onClick={() => setCurrentTab(tab)}
        >
          <h2>
            <span>{tab}</span>
          </h2>
        </li>
      )
    })}
  </ul>
)

export default Tabs
