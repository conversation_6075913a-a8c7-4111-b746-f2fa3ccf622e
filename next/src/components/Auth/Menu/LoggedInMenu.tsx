import type React from 'react'
import { useEffect, useState } from 'react'
import { PiSignOut } from 'react-icons/pi'
import Gravatar<PERSON>ithRef from '~/src/components/GravatarWithRef/GravatarWithRef'
import { auth } from '~/src/services/firebase/config'
import { logout } from '~/src/services/firebase/service'

/**
 * Logged in menu component.
 *
 * @returns {React.ReactElement} - Rendered component
 */
function LoggedInMenu(): React.ReactElement {
  // State for the user email.
  const [userEmail, setUserEmail] = useState('')

  /**
   * Get the user email.
   */
  useEffect(() => {
    const getUserEmail = async () => {
      // Get the user email from the Firebase auth object.
      const email = auth.currentUser?.email
      // Set the user email in the state.
      setUserEmail(email)
    }
    getUserEmail()
  }, [])

  /**
   * Handle the profile button click.
   */
  /*
  const handleProfile = () => {
    console.log('Redirect to profile page')
  }
   */

  /**
   * Handle the edit profile button click.
   */
  /*
  const handleEditProfile = () => {
    console.log('Redirect to edit profile page')
  }
   */

  /**
   * Handle the inbox button click.
   */
  /*
  const handleInbox = () => {
    console.log('Redirect to inbox page')
  }
   */

  /**
   * Handle the help button click.
   */
  /*
  const handleHelp = () => {
    console.log('Redirect to help page')
  }
   */

  /**
   * Handle the logout button click.
   */
  const handleLogout = async () => {
    logout()
  }

  return (
    <>
      <GravatarWithRef
        email={userEmail}
        className="relative inline-block cursor-pointer rounded-full
              object-cover object-center"
        size={30}
      />
      <ul
        role="menu"
        className="border-blue-gray-50 font-sans text-blue-gray-500
              shadow-blue-gray-500/10 absolute right-0 z-10
              flex min-w-[180px] origin-top scale-0 transform flex-col
              gap-2 overflow-auto rounded-md border bg-white
              p-3 text-sm font-normal shadow-lg transition
              duration-150 ease-in-out focus:outline-none group-hover:scale-100"
      >
        {/* Menu items
        <button
          type="button"
          tabIndex={-1}
          role="menuitem"
          onClick={handleProfile}
          disabled={true}
          className="hover:bg-blue-gray-50 hover:text-blue-gray-900 focus:bg-blue-gray-50 focus:text-blue-gray-900 active:bg-blue-gray-50 active:text-blue-gray-900 flex w-full cursor-pointer select-none items-center gap-2 rounded-md px-3 pb-2 pt-[9px] text-start leading-tight outline-none transition-all hover:bg-opacity-80 focus:bg-opacity-80 active:bg-opacity-80"
        >
          <FaRegUserCircle className="h-4 w-4" size={4} />
          <p className="font-sans block text-sm font-normal leading-normal text-inherit antialiased">
            My Profile
          </p>
        </button>
        <button
          type="button"
          tabIndex={-1}
          role="menuitem"
          onClick={handleEditProfile}
          disabled={true}
          className="hover:bg-blue-gray-50 hover:text-blue-gray-900 focus:bg-blue-gray-50 focus:text-blue-gray-900 active:bg-blue-gray-50 active:text-blue-gray-900 flex w-full cursor-pointer select-none items-center gap-2 rounded-md px-3 pb-2 pt-[9px] text-start leading-tight outline-none transition-all hover:bg-opacity-80 focus:bg-opacity-80 active:bg-opacity-80"
        >
          <RxGear className="h-4 w-4" size={4} />
          <p className="font-sans block text-sm font-normal leading-normal text-inherit antialiased">
            Edit Profile
          </p>
        </button>
        <button
          type="button"
          tabIndex={-1}
          role="menuitem"
          onClick={handleInbox}
          disabled={true}
          className="hover:bg-blue-gray-50 hover:text-blue-gray-900 focus:bg-blue-gray-50 focus:text-blue-gray-900 active:bg-blue-gray-50 active:text-blue-gray-900 flex w-full cursor-pointer select-none items-center gap-2 rounded-md px-3 pb-2 pt-[9px] text-start leading-tight outline-none transition-all hover:bg-opacity-80 focus:bg-opacity-80 active:bg-opacity-80"
        >
          <HiOutlineInboxIn className="h-4 w-4" size={4} />
          <p className="font-sans block text-sm font-normal leading-normal text-inherit antialiased">
            Inbox
          </p>
        </button>
        <button
          type="button"
          tabIndex={-1}
          role="menuitem"
          onClick={handleHelp}
          disabled={true}
          className="hover:bg-blue-gray-50 hover:text-blue-gray-900 focus:bg-blue-gray-50 focus:text-blue-gray-900 active:bg-blue-gray-50 active:text-blue-gray-900 flex w-full cursor-pointer select-none items-center gap-2 rounded-md px-3 pb-2 pt-[9px] text-start leading-tight outline-none transition-all hover:bg-opacity-80 focus:bg-opacity-80 active:bg-opacity-80"
        >
          <IoHelpCircleOutline className="h-4 w-4" size={4} />
          <p className="font-sans block text-sm font-normal leading-normal text-inherit antialiased">
            Help
          </p>
        </button>
        <hr
          className="border-blue-gray-50 my-2"
          tabIndex={-1}
          role="menuitem"
        />
        */}
        <button
          type="button"
          tabIndex={-1}
          role="menuitem"
          onClick={handleLogout}
          className="hover:bg-blue-gray-50 hover:text-blue-gray-900 focus:bg-blue-gray-50 focus:text-blue-gray-900 active:bg-blue-gray-50 active:text-blue-gray-900 flex w-full cursor-pointer select-none items-center gap-2 rounded-md px-3 pb-2 pt-[9px] text-start leading-tight outline-none transition-all hover:bg-opacity-80 focus:bg-opacity-80 active:bg-opacity-80"
        >
          <PiSignOut className="h-4 w-4" size={4} />
          <p className="font-sans block text-sm font-normal leading-normal text-inherit antialiased">
            Sign Out
          </p>
        </button>
      </ul>
    </>
  )
}

export default LoggedInMenu
