import Link from 'next/link'
import type { Icons, SectionItems } from '~/src/types'
import Icon from '../../Icon/Icon'
import KitcoIcon from '../../Icon/KitcoIcon'
import StorageIcon from '../../Icon/StorageIcon'
import styles from './SectionList.module.scss'

interface Props {
  title: string
  titleUrl?: string
  icon?: Icons | 'kitco' | 'storage'
  iconColor?: string
  items?: SectionItems[]
  isExternalLink?: boolean
}

const SectionListTwo = ({
  icon,
  iconColor,
  items,
  title,
  titleUrl,
  isExternalLink = false,
}: Props) => {
  const defaultColor = '#f9c432'
  return (
    <div className={styles.iconTitleContainer}>
      {!icon && <div />}
      {icon === 'kitco' && (
        <div className={styles.iconContainer}>
          <KitcoIcon color={!iconColor ? defaultColor : iconColor} />
        </div>
      )}
      {icon === 'storage' && (
        <div className={styles.iconContainer}>
          <StorageIcon color={!iconColor ? defaultColor : iconColor} />
        </div>
      )}
      {icon !== 'kitco' && icon !== 'storage' && icon !== undefined && (
        <div className={styles.iconContainer}>
          <Icon
            icon={icon}
            size="18px"
            color={!iconColor ? defaultColor : iconColor}
          />
        </div>
      )}
      <ul>
        <li>
          {!titleUrl ? (
            <h4 className="font-mulish mb-1 pt-1 text-sm leading-5 text-white opacity-90">
              {title}
            </h4>
          ) : (
            <Link href={titleUrl} target={isExternalLink ? '_blank' : '_self'}>
              <h4 className="font-mulish mb-1 pt-1 text-sm leading-5 text-white hover:underline">
                {title}
              </h4>
            </Link>
          )}
        </li>
        {!items && <div />}
        {items?.map((x, idx) => (
          <li
            key={idx}
            className="font-mulish text-sm leading-5 text-[#ededed] hover:text-white"
          >
            {!x.as ? (
              <a href={x.href} className={styles.item}>
                {x.name}
              </a>
            ) : (
              <Link
                className={styles.item}
                href={x.href}
                as={x.as}
                target={x?.isExternalLink ? '_blank' : '_self'}
              >
                {x.name}
              </Link>
            )}
          </li>
        ))}
      </ul>
    </div>
  )
}

export default SectionListTwo
