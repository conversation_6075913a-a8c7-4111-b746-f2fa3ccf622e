import { type FC, useState } from 'react'
import type { RegionIndicesQuery } from '~/src/generated'
import BlockShell from '../BlockShell/BlockShell'
import styles from './MarketIndices.module.scss'
import RegionList from './RegionList'
import Tabs from './Tabs'

type Region = 'US' | 'EU' | 'ASIA'

const MarketIndices: FC<{ data: RegionIndicesQuery }> = ({ data }) => {
  const [region, setRegion] = useState<Region>('US')

  return (
    <BlockShell title="Market Indices" href="/markets">
      <div className={styles.wrapper}>
        <div className={styles.contentsContainer}>
          <Tabs region={region} setRegion={setRegion} />
          <div>
            {region === 'US' && <RegionList quotes={data?.USquotes?.results} />}
            {region === 'EU' && (
              <RegionList
                quotes={data?.EUquotes?.results.filter(
                  (v) => v.name !== 'DAX Stock Index',
                )}
              />
            )}
            {region === 'ASIA' && (
              <RegionList quotes={data?.ASIAquotes?.results} />
            )}
          </div>
          <p className={styles.delayText}>Index data delayed by 10 minutes</p>
        </div>
      </div>
    </BlockShell>
  )
}

export default MarketIndices
