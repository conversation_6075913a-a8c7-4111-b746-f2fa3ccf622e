import clsx from 'clsx'
import type React from 'react'
import { type ReactNode, Suspense } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import * as VideoTeaser from '~/src/components/VideoTeaser/VideoTeaser'
import { SocialsKitco } from '~/src/components/socials/socials-kitco.component'
import { ContentWrapper } from '../ContentWrapper/ContentWrapper.component'
import { LittleVideoTeaserLoading } from '../LittleVideoTeaser/LittleVideoTeaser.component'
import styles from './VideoPagesTopSection.module.scss'

interface SectionProps {
  children?: ReactNode
}

export const Section: React.FC<SectionProps> = ({ children }) => {
  return (
    <section className="bg-[#192732] px-0 pb-4 text-white lg:px-10 lg:py-10">
      <ContentWrapper
        className={clsx('grid grid-cols-1 gap-6', styles.videosSection)}
      >
        {children}
      </ContentWrapper>
    </section>
  )
}

export const FeaturedVideo: React.FC<{
  isFetching?: boolean
  node: any
}> = ({ node, isFetching }) => {
  return (
    <ErrBoundary>
      <div className="col-span-1 md:col-span-2">
        <VideoTeaser.CtxProvider
          node={node}
          isFetching={isFetching}
          key={node?.id}
        >
          <div className="flex flex-col gap-3">
            <Suspense fallback={<div />}>
              <VideoTeaser.Player />
            </Suspense>
            <VideoTeaser.Category />
            <VideoTeaser.A>
              <VideoTeaser.Title className="!text-2xl !leading-[130%] sm:!text-white" />
            </VideoTeaser.A>
            <VideoTeaser.Timestamp />
            <VideoTeaser.Description />
            <div className="flex flex-col justify-between gap-6 md:flex-row md:gap-1">
              <div className="sm:!text-white">
                <VideoTeaser.Guests />
                <VideoTeaser.Source />
              </div>
              <ShareLinks />
            </div>
          </div>
        </VideoTeaser.CtxProvider>
      </div>
    </ErrBoundary>
  )
}

function ShareLinks() {
  return (
    <div className="flex items-center gap-3">
      <h5 className="underline sm:!text-white">Share</h5>
      <SocialsKitco className="sm:!text-white" hidePrint={true} />
    </div>
  )
}

export const UpNext: React.FC<{
  nodes: Array<any>
  isFetching?: boolean
}> = ({ nodes, isFetching }) => {
  if (isFetching) {
    return (
      <UpNextWrapper>
        <div className="mx-auto mb-4 mt-4 h-[100px] w-[320px] tablet:mt-0" />
        {Array.from(Array(5).keys()).map((_, idx: number) => (
          <LittleVideoTeaserLoading key={idx} />
        ))}
      </UpNextWrapper>
    )
  }
  return (
    <UpNextWrapper>
      <AdvertisingSlot
        id={'vid-banner'}
        className="mx-auto mb-4 mt-4 h-[100px] w-[320px] tablet:mt-0"
      />
      {!nodes?.length ? (
        <p>No results found</p>
      ) : (
        nodes?.map((video) => (
          <li
            key={video?.id}
            className="relative grid w-full grid-cols-3 gap-4"
          >
            <VideoTeaser.CtxProvider node={video} key={video.id}>
              <VideoTeaser.A className="aspect-video">
                <VideoTeaser.Img
                  height={132}
                  width={240}
                  className="block rounded-[0]"
                />
                <VideoTeaser.PlayButton />
              </VideoTeaser.A>
              <div className="col-span-2 block">
                <VideoTeaser.Category />
                <VideoTeaser.A className="block">
                  <VideoTeaser.Title />
                </VideoTeaser.A>
              </div>
            </VideoTeaser.CtxProvider>
          </li>
        ))
      )}
    </UpNextWrapper>
  )
}

const UpNextWrapper: React.FC<{ children?: React.ReactNode }> = ({
  children,
}) => {
  return (
    <div className="flex flex-col border-t border-t-white/10 pt-6 md:border-t-0 md:pt-0 ">
      <h4 className="pb-6 text-xl uppercase leading-4">Up next</h4>
      <ul className="mb-8 flex flex-col gap-4">{children}</ul>
      <div className="flex h-full flex-col items-center justify-center">
        <AdvertisingSlot
          id={'right-rail-1'}
          className="mx-auto mb-10 h-[250px] w-[300px] desktop:mb-4"
        />
      </div>
    </div>
  )
}
