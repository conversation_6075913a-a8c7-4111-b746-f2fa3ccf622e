import React, { forwardRef } from 'react'
import Gravatar from 'react-gravatar'

/**
 * Props for the GravatarWithRef component.
 * @typedef {Object} GravatarProps
 * @property {string} email - The email used to generate the Gravatar.
 * @property {number} [size=250] - The size of the Gravatar image.
 * @property {string} [className] - Additional CSS classes for styling.
 */
interface GravatarProps {
  email: string
  size?: number
  className?: string
}

/**
 * A wrapper for the react-gravatar component that supports `ref` using forwardRef.
 *
 * @param {GravatarProps} props - The props for the Gravatar component.
 * @param {React.Ref<HTMLDivElement>} ref - A ref to the container div.
 * @returns {JSX.Element} A Gravatar image wrapped in a div.
 */
const GravatarWithRef = forwardRef<HTMLDivElement, GravatarProps>(
  ({ email, size = 250, className, ...props }, ref) => {
    return React.createElement(
      'div',
      { ref, ...props },
      React.createElement(Gravatar as any, { email, size, className }),
    )
  },
)

GravatarWithRef.displayName = 'GravatarWithRef'

export default GravatarWithRef
