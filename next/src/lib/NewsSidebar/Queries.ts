import type { UseQueryOptions } from '@tanstack/react-query'
// @ts-ignore
import { gql } from 'graphql-request'
import type {
  LatestNewsSidebarByCategoryQuery,
  LatestNewsSidebarByCategoryQueryVariables,
  LatestNewsSidebarQuery,
  LatestNewsSidebarQueryVariables,
} from '~/src/generated'
import { graphs } from '~/src/services/database/fetcher'
import type QueryArgs from '~/src/types/QueryArgs'

export const newsSidebar = {
  latestNewsSidebarByCategory: (
    args: QueryArgs<
      LatestNewsSidebarByCategoryQueryVariables,
      LatestNewsSidebarByCategoryQuery
    >,
  ): UseQueryOptions<LatestNewsSidebarByCategoryQuery> => {
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['latestNewsSidebarByCategory', args.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query LatestNewsSidebarByCategory(
              $urlAlias: String!
              $limit: Int
              $offset: Int
              $includeRelatedCategories: Boolean
              $includeEntityQueues: Boolean
            ) {
              nodeListByCategory(
                limit: $limit
                offset: $offset
                urlAlias: $urlAlias
                includeRelatedCategories: $includeRelatedCategories
                includeEntityQueues: $includeEntityQueues
              ) {
                items {
                  ... on NewsArticle {
                    id
                    title
                    urlAlias
                    updatedAt
                    legacyThumbnailImageUrl
                    category {
                      name
                      urlAlias
                    }
                    teaserImage {
                      detail {
                        default {
                          srcset
                        }
                      }
                    }
                    image {
                      detail {
                        default {
                          srcset
                        }
                      }
                    }
                  }
                  ... on OffTheWire {
                    id
                    title
                    urlAlias
                    updatedAt
                    legacyThumbnailImageUrl
                    category {
                      name
                      urlAlias
                    }
                    teaserImage {
                      detail {
                        default {
                          srcset
                        }
                      }
                    }
                    image {
                      detail {
                        default {
                          srcset
                        }
                      }
                    }
                  }
                  ... on Commentary {
                    id
                    title
                    urlAlias
                    updatedAt
                    legacyThumbnailImageUrl
                    category {
                      name
                      urlAlias
                    }
                    teaserImage {
                      detail {
                        default {
                          srcset
                        }
                      }
                    }
                    image {
                      detail {
                        default {
                          srcset
                        }
                      }
                    }
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  latestNewsSidebar: (
    args: QueryArgs<LatestNewsSidebarQueryVariables, LatestNewsSidebarQuery>,
  ): UseQueryOptions<LatestNewsSidebarQuery> => {
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['latestNewsSidebar'],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query latestNewsSidebar($limit: Int, $offset: Int) {
              queue: nodeListQueue(
                limit: $limit
                offset: $offset
                bundles: [NewsArticle, OffTheWire, Commentary]
                queueId: "latest_news"
              ) {
                items {
                  ... on Commentary {
                    id
                    title
                    category {
                      name
                      urlAlias
                    }
                    urlAlias
                    updatedAt
                    image {
                      detail {
                        default {
                          srcset
                        }
                      }
                    }
                    teaserImage {
                      detail {
                        default {
                          srcset
                        }
                      }
                    }
                    legacyThumbnailImageUrl
                  }
                  ... on NewsArticle {
                    id
                    title
                    category {
                      name
                      urlAlias
                    }
                    urlAlias
                    updatedAt
                    image {
                      detail {
                        default {
                          srcset
                        }
                      }
                    }
                    teaserImage {
                      detail {
                        default {
                          srcset
                        }
                      }
                    }
                    legacyThumbnailImageUrl
                  }
                  ... on OffTheWire {
                    id
                    title
                    category {
                      name
                      urlAlias
                    }
                    urlAlias
                    updatedAt
                    image {
                      detail {
                        default {
                          srcset
                        }
                      }
                    }
                    teaserImage {
                      detail {
                        default {
                          srcset
                        }
                      }
                    }
                    legacyThumbnailImageUrl
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },
}
