import type { FC, ReactNode } from 'react'
import cs from '~/src/utils/cs'

export const NewsContentWrapper: FC<{
  children: ReactNode | ReactNode[]
}> = ({ children }) => (
  <div
    id="newsContentWrapper"
    className={cs(['lx:px-0 mx-auto w-auto px-5 md:px-10'])}
  >
    {children}
  </div>
)

// this has overflow for the video section and carousel
export const NewsContentWrapperWithOverflow: FC<{
  children: ReactNode | ReactNode[]
}> = ({ children }) => (
  <div className={cs(['mx-auto w-auto max-w-[1240px] px-6 md:px-10 2xl:px-0'])}>
    {children}
  </div>
)
