import * as Navigation from '../Composables'
import JewelerMenu from './JewelerMenu'

const JewelerItem = ({ value, onNodeUpdate }) => {
  return (
    <Navigation.Item value={value}>
      <Navigation.Trigger value={value} onNodeUpdate={onNodeUpdate}>
        <div className="whitespace-nowrap text-sm font-bold leading-5 text-white">
          <span className="group-hover:underline">{value}</span>
        </div>
      </Navigation.Trigger>
      <Navigation.Content>
        <JewelerMenu />
      </Navigation.Content>
    </Navigation.Item>
  )
}

export default JewelerItem
