import type { SectionItems } from '~/src/types'
import SectionList from '../SectionList/SectionList'
import * as Navigation from './../Composables'

export const charts: SectionItems[] = [
  {
    name: 'Gold',
    href: '/charts/gold',
    as: '/charts/gold',
  },
  {
    name: 'Silver',
    href: '/charts/[commodity]',
    as: '/charts/silver',
  },
  {
    name: 'Platinum',
    href: '/charts/[commodity]',
    as: '/charts/platinum',
  },
  {
    name: 'Palladium',
    href: '/charts/[commodity]',
    as: '/charts/palladium',
  },
]

export const cryptos: SectionItems[] = [
  {
    name: 'Bitcoin',
    href: '/price/crypto/[name]',
    as: '/price/crypto/bitcoin',
  },
  {
    name: 'Ethereum',
    href: '/price/crypto/[name]',
    as: '/price/crypto/ethereum',
  },
  {
    name: '<PERSON><PERSON><PERSON><PERSON>',
    href: '/price/crypto/[name]',
    as: '/price/crypto/litecoin',
  },
  {
    name: 'Binance Coin',
    href: '/price/crypto/[name]',
    as: '/price/crypto/binance-coin',
  },
  {
    name: 'Ripple',
    href: '/price/crypto/[name]',
    as: '/price/crypto/xrp',
  },
  {
    name: 'Tether',
    href: '/price/crypto/[name]',
    as: '/price/crypto/tether',
  },
  {
    name: 'Cardano',
    href: '/price/crypto/[name]',
    as: '/price/crypto/cardano',
  },
  {
    name: 'Polkadot',
    href: '/price/crypto/[name]',
    as: '/price/crypto/polkadot',
  },
  {
    name: 'Dogecoin',
    href: '/price/crypto/[name]',
    as: '/price/crypto/dogecoin',
  },
  {
    name: 'More Cryptos',
    href: '/price/crypto/',
    as: '/price/crypto/',
  },
]

export const forYou: SectionItems[] = []

const ChartsMenu = () => {
  return (
    <Navigation.SubMenuGrid>
      <Navigation.SubMenuColumn>
        <SectionList title="Charts & Prices" items={cryptos} />
      </Navigation.SubMenuColumn>
    </Navigation.SubMenuGrid>
  )
}

export default ChartsMenu
