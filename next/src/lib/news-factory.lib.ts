import type { UseQueryOptions } from '@tanstack/react-query'
import { gql } from 'graphql-request'
import type {
  ArticleByAliasQuery,
  ArticleByAliasQueryVariables,
  AuthorByUrlAliasQuery,
  AuthorByUrlAliasQueryVariables,
  BreakingNewsQuery,
  BreakingNewsQueryVariables,
  CommentaryListFilterCommentariessQuery,
  CommentaryListFilterCommentariessQueryVariables,
  GuestByUrlAliasQuery,
  GuestByUrlAliasQueryVariables,
  LatestNewsSidebarQuery,
  LatestNewsSidebarQueryVariables,
  MarketNewsHomePageQuery,
  MarketNewsHomePageQueryVariables,
  NewsByCategoryGenericQuery,
  NewsByCategoryGenericQueryVariables,
  NewsCategoriesTreeQuery,
  NewsCategoriesTreeQueryVariables,
  NewsCategoryChildrenQuery,
  NewsCategoryChildrenQueryVariables,
  NewsGenericByTagQuery,
  NewsGenericByTagQueryVariables,
  NewsGenericCommentariesQuery,
  NewsGenericCommentariesQueryVariables,
  NewsGenericPressReleasesQuery,
  NewsGenericPressReleasesQueryVariables,
  NewsIndexPageQueryQuery,
  NewsIndexPageQueryQueryVariables,
  NewsLeadGenQueryQuery,
  NewsLeadGenQueryQueryVariables,
  NewsOffTheWireQuery,
  NewsOffTheWireQueryVariables,
  NewsOpinionsGenericQuery,
  NewsOpinionsGenericQueryVariables,
  NewsOtwListQuery,
  NewsOtwListQueryVariables,
  NewsTopContributorsQuery,
  NewsTopContributorsQueryVariables,
  NewsTrendingGenericQuery,
  NewsTrendingGenericQueryVariables,
  NewsTrendingTagsQuery,
  NewsTrendingTagsQueryVariables,
  NodeListByAuthorQuery,
  NodeListByAuthorQueryVariables,
  NodeListByGuestQuery,
  NodeListByGuestQueryVariables,
  NodeListBySponsorQuery,
  NodeListBySponsorQueryVariables,
  NodeListByTagQuery,
  NodeListByTagQueryVariables,
  NodeListNewsFeedQuery,
  NodeListNewsFeedQueryVariables,
  NodeListQueueCommentariesQuery,
  NodeListQueueCommentariesQueryVariables,
  NodeListQueueQuery,
  NodeListQueueQueryVariables,
  ReportersQuery,
  ReportersQueryVariables,
  SearchMetalsQuery,
  SearchMetalsQueryVariables,
  SearchNewsQuery,
  SearchNewsQueryVariables,
  SponsorByUrlAliasQuery,
  SponsorByUrlAliasQueryVariables,
  SponsoredContentQuery,
  SponsoredContentQueryVariables,
  StreetNewsHomePageQuery,
  StreetNewsHomePageQueryVariables,
  TagByUrlAliasQuery,
  TagByUrlAliasQueryVariables,
} from '~/src/generated'
import type QueryArgs from '~/src/types/QueryArgs'
import { graphs } from '../services/database/fetcher'
import { metalFragment, metalQuoteFragment } from './metals-fragments.graphql'
import {
  articleTeaserFragment,
  authorFragment,
  categoryFragment,
  commentaryTeaserFragment,
  imageFragment,
  offTheWireFragment,
  sourceFragment,
} from './news-fragments.graphql'

export const news = {
  newsByCategoryGeneric: (
    args: QueryArgs<
      NewsByCategoryGenericQueryVariables,
      NewsByCategoryGenericQuery
    >,
  ): UseQueryOptions<NewsByCategoryGenericQuery> => {
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['newsByCategoryGeneric', args.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            ${articleTeaserFragment}
            ${offTheWireFragment}
            ${imageFragment}
            ${categoryFragment}
            query NewsByCategoryGeneric(
              $urlAlias: String!
              $limit: Int
              $offset: Int
              $includeRelatedCategories: Boolean
              $includeEntityQueues: Boolean
            ) {
              nodeListByCategory(
                limit: $limit
                offset: $offset
                urlAlias: $urlAlias
                includeRelatedCategories: $includeRelatedCategories
                includeEntityQueues: $includeEntityQueues
              ) {
                total
                items {
                  ... on NewsArticle {
                    ...ArticleTeaserFragment
                  }

                  ... on OffTheWire {
                    ...OffTheWireFragment
                  }

                  ... on Commentary {
                    __typename
                    title
                    author {
                      authorWebsite
                      body
                      email
                      facebookId
                      name
                      imageUrl
                      linkedInId
                      title
                      twitterId
                      authorType
                      urlAlias
                      roles
                    }
                    bodyWithEmbeddedMedia {
                      value
                      embeddedMedia {
                        assetUuid
                        snippetUuid
                        status
                        startTime
                        endTime
                        type
                        thumbnailUuid
                      }
                    }
                    category {
                      ...CategoryFragment
                    }
                    summaryBullets
                    supportingAuthors {
                      id
                      name
                      urlAlias
                      imageUrl
                      twitterId
                      linkedInId
                      email
                      body
                    }
                    featuredContent {
                      type
                      assetUuid
                      snippetUuid
                      status
                      startTime
                      endTime
                      thumbnailUuid
                    }
                    urlAlias
                    source {
                      id
                      name
                      description
                      subtitle
                    }
                    teaserSnippet
                    image {
                      ...ImageFragment
                    }
                    audioTts {
                      isPublished
                      assetUuid
                      status
                      endTime
                      startTime
                    }
                    tags {
                      id
                      name
                      urlAlias
                    }
                    legacyThumbnailImageUrl
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  newsOTWList: (
    args: QueryArgs<NewsOtwListQueryVariables, NewsOtwListQuery>,
  ): UseQueryOptions<NewsOtwListQuery> => {
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['newsOTWList'],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            ${imageFragment}
            query NewsOTWList($limit: Int, $offset: Int) {
              nodeList(limit: $limit, offset: $offset, bundles: [OffTheWire]) {
                total
                items {
                  ... on OffTheWire {
                    id
                    title
                    teaserHeadline
                    urlAlias
                    createdAt
                    updatedAt
                    author {
                      email
                      name
                      imageUrl
                      urlAlias
                    }
                    source {
                      id
                      description
                      name
                    }
                    teaserSnippet
                    legacyThumbnailImageUrl
                    image {
                      ...ImageFragment
                    }
                    featuredContent {
                      type
                      assetUuid
                      snippetUuid
                      status
                      startTime
                      endTime
                      thumbnailUuid
                    }
                    tags {
                      id
                      name
                      urlAlias
                    }
                    audioTts {
                      isPublished
                      assetUuid
                      status
                      endTime
                      startTime
                    }
                    summaryBullets
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  newsOffTheWire: (
    args: QueryArgs<NewsOffTheWireQueryVariables, NewsOffTheWireQuery>,
  ): UseQueryOptions<NewsOffTheWireQuery> => {
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['newsOffTheWire', args.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query NewsOffTheWire(
              $urlAlias: String!
              $limit: Int
              $offset: Int
            ) {
              nodeListByCategory(
                limit: $limit
                offset: $offset
                urlAlias: $urlAlias
              ) {
                total
                items {
                  ... on OffTheWire {
                    id
                    title
                    teaserHeadline
                    urlAlias
                    createdAt
                    updatedAt
                    author {
                      email
                      name
                      imageUrl
                      urlAlias
                    }
                    source {
                      id
                      description
                      name
                    }
                    teaserSnippet
                    legacyThumbnailImageUrl
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  newsTrending: (
    args?: QueryArgs<
      NewsTrendingGenericQueryVariables,
      NewsTrendingGenericQuery
    >,
  ): UseQueryOptions<NewsTrendingGenericQuery> => {
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['newsTrending', args?.variables?.limit],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            ${articleTeaserFragment}
            ${imageFragment}
            query NewsTrendingGeneric($limit: Int) {
              nodeListTrending(
                bundles: [NewsArticle]
                sort: Week
                limit: $limit
              ) {
                ... on NewsArticle {
                  ...ArticleTeaserFragment
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  newsOpinionsList: (
    args: QueryArgs<
      NewsOpinionsGenericQueryVariables,
      NewsOpinionsGenericQuery
    >,
  ): UseQueryOptions<NewsOpinionsGenericQuery> => {
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['newsOpinionsList'],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            ${commentaryTeaserFragment}
            ${imageFragment}
            query NewsOpinionsGeneric($limit: Int, $offset: Int) {
              opinions: nodeList(
                limit: $limit
                offset: $offset
                bundles: [Commentary]
              ) {
                items {
                  ... on Commentary {
                    ...CommentaryTeaserFragment
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  nodeListPressReleases: (
    args: QueryArgs<
      NewsGenericPressReleasesQueryVariables,
      NewsGenericPressReleasesQuery
    >,
  ): UseQueryOptions<NewsGenericPressReleasesQuery> => {
    // @ts-ignore
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['nodeListPressReleases', args?.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query NewsGenericPressReleasesUhm($limit: Int, $offset: Int) {
              nodeList(
                limit: $limit
                offset: $offset
                bundles: [PressRelease]
              ) {
                items {
                  ... on PressRelease {
                    id
                    title
                    teaserHeadline
                    createdAt
                    updatedAt
                    url
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  nodeListPressReleasesQueue: (
    args: QueryArgs<NodeListQueueQueryVariables, NodeListQueueQuery>,
  ): UseQueryOptions<NodeListQueueQuery> => {
    // @ts-ignore
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['nodeListPressReleases', args?.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query NewsGenericPressReleases(
              $limit: Int
              $offset: Int
              $queueId: String = "press_releases"
            ) {
              queue: nodeListQueue(
                limit: $limit
                offset: $offset
                queueId: $queueId
                bundles: [PressRelease]
              ) {
                items {
                  ... on PressRelease {
                    id
                    title
                    teaserHeadline
                    createdAt
                    updatedAt
                    url
                  }
                }
              }

              ids: nodeIdsInQueue(queueId: $queueId)
            }
          `,
          args?.variables,
        ),
    }
  },

  nodeListQueue: (
    args: QueryArgs<NodeListQueueQueryVariables, NodeListQueueQuery>,
  ): UseQueryOptions<NodeListQueueQuery> => {
    // @ts-ignore
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['nodeListQueue', args.variables?.queueId],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            ${articleTeaserFragment}
            ${commentaryTeaserFragment}
            ${imageFragment}
            ${authorFragment}
            query NodeListQueue($limit: Int, $offset: Int, $queueId: String!) {
              queue: nodeListQueue(
                limit: $limit
                offset: $offset
                queueId: $queueId
                bundles: [NewsArticle]
              ) {
                total
                items {
                  ... on NewsArticle {
                    ...ArticleTeaserFragment
                  }

                  ... on Commentary {
                    ...CommentaryTeaserFragment
                    category {
                      id
                      name
                      urlAlias
                    }
                  }
                }
              }

              ids: nodeIdsInQueue(queueId: $queueId)
            }
          `,
          args?.variables,
        ),
    }
  },

  nodeListNewsFeed: (
    args: QueryArgs<NodeListNewsFeedQueryVariables, NodeListNewsFeedQuery>,
  ): UseQueryOptions<NodeListNewsFeedQuery> => {
    // @ts-ignore
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['nodeListNewsFeed', args.variables?.queueId],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            ${articleTeaserFragment}
            ${imageFragment}
            query NodeListNewsFeed(
              $limit: Int
              $offset: Int
              $queueId: String!
            ) {
              nodeListNewsFeed(
                limit: $limit
                offset: $offset
                queueId: $queueId
                bundles: [NewsArticle, Commentary, OffTheWire]
              ) {
                total
                items {
                  ... on NewsArticle {
                    ...ArticleTeaserFragment
                  }
                  ... on Commentary {
                    id
                    __typename
                    title
                    featured
                    teaserHeadline
                    urlAlias
                    source {
                      name
                      subtitle
                      description
                    }
                    createdAt
                    updatedAt
                  }
                  ... on OffTheWire {
                    id
                    __typename
                    title
                    featured
                    teaserHeadline
                    urlAlias
                    source {
                      name
                      subtitle
                      description
                    }
                    createdAt
                    updatedAt
                  }
                }
              }

              ids: nodeIdsInQueue(queueId: $queueId)
            }
          `,
          args?.variables,
        ),
    }
  },

  commentaryListFilterCommentaries: (
    args: QueryArgs<
      CommentaryListFilterCommentariessQueryVariables,
      CommentaryListFilterCommentariessQuery
    >,
  ): UseQueryOptions<CommentaryListFilterCommentariessQuery> => {
    // @ts-ignore
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['nodeListQueueCommentariess', args?.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            ${commentaryTeaserFragment}
            ${imageFragment}
            ${authorFragment}
            query CommentaryListFilterCommentariess(
              $limit: Int
              $offset: Int
              $opinionType: OpinionType!
            ) {
              commentaries: commentaryListQueue(
                limit: $limit
                offset: $offset
                queueId: "commentaries"
                opinionType: $opinionType
              ) {
                total
                items {
                  ... on Commentary {
                    ...CommentaryTeaserFragment
                  }
                }
              }
              ids: nodeIdsInQueue(queueId: "commentaries")
            }
          `,
          args?.variables,
        ),
    }
  },

  nodeListQueueCommentaries: (
    args: QueryArgs<
      NodeListQueueCommentariesQueryVariables,
      NodeListQueueCommentariesQuery
    >,
  ): UseQueryOptions<NodeListQueueCommentariesQuery> => {
    // @ts-ignore
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['nodeListQueueCommentaries'],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            ${commentaryTeaserFragment}
            ${imageFragment}
            ${authorFragment}
            query NodeListQueueCommentaries($limit: Int, $offset: Int) {
              commentaries: nodeListQueue(
                limit: $limit
                offset: $offset
                queueId: "commentaries"
                bundles: [Commentary]
              ) {
                total
                items {
                  ... on Commentary {
                    ...CommentaryTeaserFragment
                  }
                }
              }
              ids: nodeIdsInQueue(queueId: "commentaries")
            }
          `,
          args?.variables,
        ),
    }
  },

  marketNews: (
    args: QueryArgs<MarketNewsHomePageQueryVariables, MarketNewsHomePageQuery>,
  ): UseQueryOptions<MarketNewsHomePageQuery> => {
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['marketNews'],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            ${sourceFragment}
            query MarketNewsHomePage($limit: Int, $offset: Int) {
              marketNews(limit: $limit, offset: $offset) {
                total
                items {
                  ... on NewsArticle {
                    id
                    title
                    teaserHeadline
                    urlAlias
                    source {
                      ...SourceFragment
                    }
                    createdAt
                    updatedAt
                    legacyThumbnailImageUrl
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  newsByTag: (
    args: QueryArgs<NewsGenericByTagQueryVariables, NewsGenericByTagQuery>,
  ): UseQueryOptions<NewsGenericByTagQuery> => {
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['newsByTag'],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            ${articleTeaserFragment}
            query NewsGenericByTag(
              $limit: Int
              $offset: Int
              $urlAlias: String!
            ) {
              nodeListByTag(
                limit: $limit
                offset: $offset
                urlAlias: $urlAlias
              ) {
                items {
                  ... on NewsArticle {
                    ...ArticleTeaserFragment
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  newsCommentaries: (
    args: QueryArgs<
      NewsGenericCommentariesQueryVariables,
      NewsGenericCommentariesQuery
    >,
  ): UseQueryOptions<NewsGenericCommentariesQuery> => {
    // @ts-ignore
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['newsCommentaries', args?.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            ${sourceFragment}
            ${imageFragment}
            query NewsGenericCommentaries($limit: Int, $offset: Int) {
              commentaries: nodeListQueue(
                limit: $limit
                offset: $offset
                queueId: "commentaries"
                bundles: [Commentary]
              ) {
                items {
                  ... on Commentary {
                    __typename
                    id
                    createdAt
                    updatedAt
                    source {
                      ...SourceFragment
                    }
                    title
                    teaserHeadline
                    image {
                      ...ImageFragment
                    }
                    category {
                      id
                      urlAlias
                      name
                    }
                    teaserSnippet
                    urlAlias
                    author {
                      authorWebsite
                      body
                      email
                      facebookId
                      name
                      imageUrl
                      linkedInId
                      title
                      twitterId
                      authorType
                      urlAlias
                      roles
                    }
                    legacyThumbnailImageUrl
                  }
                }
                total
              }

              ids: nodeIdsInQueue(queueId: "commentaries")
            }
          `,
          args?.variables,
        ),
    }
  },

  nodeByUrlAlias: (
    args: QueryArgs<ArticleByAliasQueryVariables, ArticleByAliasQuery>,
  ): UseQueryOptions<ArticleByAliasQuery> => {
    return {
      enabled: false,
      ...args?.options,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      queryKey: ['nodeByUrlAlias', args?.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            ${categoryFragment}
            ${imageFragment}
            query ArticleByAlias($urlAlias: String!, $auHash: String) {
              nodeByUrlAlias(urlAlias: $urlAlias, auHash: $auHash) {
                __typename
                createdAt
                updatedAt
                id
                published
                title

                ... on NewsArticle {
                  author {
                    authorWebsite
                    body
                    email
                    contactEmail
                    facebookId
                    name
                    imageUrl
                    linkedInId
                    title
                    twitterId
                    authorType
                    urlAlias
                    roles
                  }
                  featuredContent {
                    type
                    assetUuid
                    snippetUuid
                    status
                    startTime
                    endTime
                    thumbnailUuid
                  }
                  summaryBullets
                  bodyWithEmbeddedMedia {
                    value
                    embeddedMedia {
                      assetUuid
                      snippetUuid
                      status
                      startTime
                      endTime
                      type
                      thumbnailUuid
                    }
                  }
                  urlAlias
                  source {
                    id
                    name
                    description
                    subtitle
                  }
                  teaserSnippet
                  category {
                    ...CategoryFragment
                  }
                  image {
                    ...ImageFragment
                  }
                  audioTts {
                    isPublished
                    assetUuid
                    status
                    endTime
                    startTime
                  }
                  tags {
                    id
                    name
                    urlAlias
                  }
                  supportingAuthors {
                    id
                    name
                    urlAlias
                    imageUrl
                    twitterId
                    linkedInId
                    email
                    body
                  }
                  legacyThumbnailImageUrl
                }
                ... on Sponsored {
                  author {
                    authorWebsite
                    body
                    email
                    facebookId
                    name
                    imageUrl
                    linkedInId
                    title
                    twitterId
                    authorType
                    urlAlias
                    roles
                  }
                  bodyWithEmbeddedMedia {
                    value
                    embeddedMedia {
                      assetUuid
                      snippetUuid
                      status
                      startTime
                      endTime
                      type
                      thumbnailUuid
                    }
                  }
                  urlAlias
                  source {
                    id
                    name
                    description
                    subtitle
                  }
                  teaserSnippet
                  image {
                    ...ImageFragment
                  }
                  audioTts {
                    isPublished
                    assetUuid
                    status
                    endTime
                    startTime
                  }
                  tags {
                    id
                    name
                    urlAlias
                  }
                }
                ... on OffTheWire {
                  urlAlias
                  author {
                    authorWebsite
                    body
                    email
                    facebookId
                    name
                    imageUrl
                    linkedInId
                    title
                    twitterId
                    authorType
                    urlAlias
                    roles
                  }
                  source {
                    name
                    subtitle
                    description
                  }
                  image {
                    ...ImageFragment
                  }
                  featuredContent {
                    type
                    assetUuid
                    snippetUuid
                    status
                    startTime
                    endTime
                    thumbnailUuid
                  }
                  tags {
                    id
                    name
                    urlAlias
                  }
                  summaryBullets
                  featured
                  body
                  legacyThumbnailImageUrl
                }

                ... on LeadGen {
                  body
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  nodeListByAuthor: (
    args: QueryArgs<NodeListByAuthorQueryVariables, NodeListByAuthorQuery>,
  ): UseQueryOptions<NodeListByAuthorQuery> => {
    return {
      enabled: false,
      ...args?.options,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      queryKey: ['nodeListByAuthor', args.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            ${articleTeaserFragment}
            ${imageFragment}
            ${sourceFragment}
            ${offTheWireFragment}
            query NodeListByAuthor(
              $limit: Int
              $offset: Int
              $urlAlias: String!
            ) {
              nodeListByAuthor(
                limit: $limit
                offset: $offset
                urlAlias: $urlAlias
              ) {
                total
                items {
                  id
                  title
                  createdAt
                  updatedAt
                  __typename
                  ... on NewsArticle {
                    ...ArticleTeaserFragment
                  }
                  ... on Commentary {
                    id
                    category {
                      id
                      name
                      urlAlias
                    }
                    teaserSnippet
                    source {
                      ...SourceFragment
                    }
                    title
                    urlAlias
                    author {
                      id
                      name
                    }
                    image {
                      ...ImageFragment
                    }
                    legacyThumbnailImageUrl
                  }
                  ... on OffTheWire {
                    ...OffTheWireFragment
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  authorByUrlAlias: (
    args: QueryArgs<AuthorByUrlAliasQueryVariables, AuthorByUrlAliasQuery>,
  ): UseQueryOptions<AuthorByUrlAliasQuery> => {
    return {
      enabled: false,
      ...args?.options,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      queryKey: ['authorByUrlAlias', args.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query AuthorByUrlAlias($urlAlias: String!) {
              authorByUrlAlias(urlAlias: $urlAlias) {
                authorWebsite
                body
                email
                contactEmail
                facebookId
                name
                hidden
                id
                imageUrl
                linkedInId
                title
                authorType
                twitterId
                roles
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  newsCategoriesTree: (
    args?: QueryArgs<NewsCategoriesTreeQueryVariables, NewsCategoriesTreeQuery>,
  ): UseQueryOptions<NewsCategoriesTreeQuery> => {
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['newsCategoriesTree'],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query NewsCategoriesTree {
              categoriesTree {
                id
                name
                urlAlias
                status
                children {
                  id
                  name
                  urlAlias
                  status
                  children {
                    id
                    name
                    urlAlias
                    status
                    children {
                      id
                    }
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  sponsorByUrlAlias: (
    args: QueryArgs<SponsorByUrlAliasQueryVariables, SponsorByUrlAliasQuery>,
  ): UseQueryOptions<SponsorByUrlAliasQuery> => {
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['sponsorByUrlAlias'],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query SponsorByUrlAlias($urlAlias: String!) {
              sponsor: sponsorByUrlAlias(urlAlias: $urlAlias) {
                id
                name
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  sponsoredContent: (
    args: QueryArgs<SponsoredContentQueryVariables, SponsoredContentQuery>,
  ): UseQueryOptions<SponsoredContentQuery> => {
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['sponsoredContent', args?.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            ${imageFragment}
            query SponsoredContent($limit: Int, $offset: Int) {
              nodeList(bundles: [Sponsored], limit: $limit, offset: $offset) {
                total
                items {
                  ... on Sponsored {
                    id
                    title
                    teaserHeadline
                    teaserSnippet
                    urlAlias
                    bodyWithEmbeddedMedia {
                      value
                    }
                    createdAt
                    image {
                      ...ImageFragment
                    }
                    author {
                      id
                      name
                      urlAlias
                    }
                    legacyThumbnailImageUrl
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  reporters: (
    args?: QueryArgs<ReportersQueryVariables, ReportersQuery>,
  ): UseQueryOptions<ReportersQuery> => {
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['reporters'],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query Reporters {
              reporters {
                id
                name
                imageUrl
                urlAlias
                hidden
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  topContributors: (
    args?: QueryArgs<
      NewsTopContributorsQueryVariables,
      NewsTopContributorsQuery
    >,
  ): UseQueryOptions<NewsTopContributorsQuery> => {
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['topContributors'],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query NewsTopContributors {
              topContributors {
                id
                name
                imageUrl
                urlAlias
                hidden
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  nodeListBySponsor: (
    args: QueryArgs<NodeListBySponsorQueryVariables, NodeListBySponsorQuery>,
  ): UseQueryOptions<NodeListBySponsorQuery> => {
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['nodeListBySponsor', args.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            ${imageFragment}
            query NodeListBySponsor(
              $limit: Int
              $offset: Int
              $urlAlias: String!
            ) {
              nodeListBySponsor(
                limit: $limit
                offset: $offset
                urlAlias: $urlAlias
              ) {
                total
                items {
                  ... on Sponsored {
                    id
                    createdAt
                    updatedAt
                    title
                    urlAlias
                    teaserSnippet
                    sponsor {
                      id
                      name
                    }
                    author {
                      id
                      name
                      urlAlias
                    }
                    image {
                      ...ImageFragment
                    }
                    legacyThumbnailImageUrl
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  trendingTags: (
    args?: QueryArgs<NewsTrendingTagsQueryVariables, NewsTrendingTagsQuery>,
  ): UseQueryOptions<NewsTrendingTagsQuery> => {
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['newsTrendingTags'],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query NewsTrendingTags {
              trendingTags {
                id
                urlAlias
                name
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  newsLandingPage: (
    args: QueryArgs<NewsIndexPageQueryQueryVariables, NewsIndexPageQueryQuery>,
  ): UseQueryOptions<NewsIndexPageQueryQuery> => {
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['newsLandingPage'],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query NewsIndexPageQuery($limit: Int, $offset: Int) {
              queue: nodeListQueue(
                limit: $limit
                offset: $offset
                bundles: [NewsArticle, OffTheWire]
                queueId: "latest_news"
              ) {
                total
                items {
                  ... on Commentary {
                    id
                    title
                    __typename
                    category {
                      id
                      name
                      urlAlias
                    }
                    teaserSnippet
                    teaserHeadline
                    title
                    urlAlias
                    source {
                      name
                      subtitle
                      description
                    }
                    audioTts {
                      isPublished
                      assetUuid
                    }
                    createdAt
                    updatedAt
                    image {
                      detail {
                        default {
                          srcset
                        }
                      }
                    }
                    teaserImage {
                      detail {
                        default {
                          srcset
                        }
                      }
                    }
                    teaserImage {
                      detail {
                        default {
                          srcset
                        }
                      }
                    }
                    legacyThumbnailImageUrl
                  }
                  ... on NewsArticle {
                    id
                    title
                    __typename
                    category {
                      id
                      name
                      urlAlias
                    }
                    teaserSnippet
                    teaserHeadline
                    title
                    urlAlias
                    source {
                      name
                      subtitle
                      description
                    }
                    audioTts {
                      isPublished
                      assetUuid
                    }
                    createdAt
                    updatedAt
                    image {
                      detail {
                        default {
                          srcset
                        }
                      }
                    }
                    legacyThumbnailImageUrl
                  }
                  ... on OffTheWire {
                    id
                    title
                    __typename
                    body
                    category {
                      id
                      name
                      urlAlias
                    }
                    title
                    urlAlias
                    source {
                      name
                      subtitle
                      description
                    }
                    createdAt
                    updatedAt
                    imageUrl
                    featured
                    legacyThumbnailImageUrl
                    image {
                      detail {
                        default {
                          srcset
                        }
                      }
                    }
                    audioTts {
                      isPublished
                      assetUuid
                    }
                    teaserSnippet
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  breakingNews: (
    args: QueryArgs<BreakingNewsQueryVariables, BreakingNewsQuery>,
  ): UseQueryOptions<BreakingNewsQuery> => {
    return {
      ...args?.options,
      queryKey: ['breakingNews'],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query BreakingNews {
              nodeList(bundles: [BreakingNews]) {
                total
                items {
                  ... on BreakingNews {
                    id
                    title
                    createdAt
                    updatedAt
                    category
                    byline
                    url
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  leadGen: (
    args: QueryArgs<NewsLeadGenQueryQueryVariables, NewsLeadGenQueryQuery>,
  ): UseQueryOptions<NewsLeadGenQueryQuery> => {
    return {
      ...args?.options,
      queryKey: ['leadGen'],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query NewsLeadGenQuery {
              queue: nodeListQueue(bundles: [LeadGen], queueId: "leadgen") {
                items {
                  ... on LeadGen {
                    id
                    title
                    urlAlias
                    featured
                  }
                }
                total
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  streetNews: (
    args: QueryArgs<StreetNewsHomePageQueryVariables, StreetNewsHomePageQuery>,
  ): UseQueryOptions<StreetNewsHomePageQuery> => {
    return {
      ...args?.options,
      queryKey: ['streetNews', args?.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query StreetNewsHomePage($limit: Int, $offset: Int) {
              nodeList(limit: $limit, offset: $offset, bundles: [StreetTalk]) {
                total
                items {
                  ... on StreetTalk {
                    id
                    title
                    teaserHeadline
                    source
                    createdAt
                    updatedAt
                    url
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  categoryChildrenByUrlAlias: (
    args: QueryArgs<
      NewsCategoryChildrenQueryVariables,
      NewsCategoryChildrenQuery
    >,
  ): UseQueryOptions<NewsCategoryChildrenQuery> => {
    return {
      ...args?.options,
      queryKey: ['categoryChildrenByUrlAlias'],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query NewsCategoryChildren($urlAlias: String!) {
              categoryChildrenByUrlAlias(urlAlias: $urlAlias) {
                id
                urlAlias
                name
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  tagByUrlAlias: (
    args: QueryArgs<TagByUrlAliasQueryVariables, TagByUrlAliasQuery>,
  ): UseQueryOptions<TagByUrlAliasQuery> => {
    return {
      ...args?.options,
      queryKey: ['tagByUrlAlias', args?.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query TagByUrlAlias($urlAlias: String!) {
              tagByUrlAlias(urlAlias: $urlAlias) {
                id
                name
                urlAlias
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  nodeListByTag: (
    args: QueryArgs<NodeListByTagQueryVariables, NodeListByTagQuery>,
  ): UseQueryOptions<NodeListByTagQuery> => {
    return {
      ...args?.options,
      queryKey: ['nodeListByTag', args?.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            ${articleTeaserFragment}
            ${imageFragment}
            ${authorFragment}
            ${commentaryTeaserFragment}
            query NodeListByTag($limit: Int, $offset: Int, $urlAlias: String!) {
              nodeListByTag(
                limit: $limit
                offset: $offset
                urlAlias: $urlAlias
              ) {
                total
                items {
                  ... on NewsArticle {
                    ...ArticleTeaserFragment
                  }

                  ... on Commentary {
                    ...CommentaryTeaserFragment
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  guestByUrlAlias: (
    args: QueryArgs<GuestByUrlAliasQueryVariables, GuestByUrlAliasQuery>,
  ): UseQueryOptions<GuestByUrlAliasQuery> => {
    return {
      ...args?.options,
      queryKey: ['guestByUrlAlias'],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query GuestByUrlAlias($urlAlias: String!) {
              guest: guestByUrlAlias(urlAlias: $urlAlias) {
                id
                fullName
                urlAlias
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  nodeListByGuest: (
    args: QueryArgs<NodeListByGuestQueryVariables, NodeListByGuestQuery>,
  ): UseQueryOptions<NodeListByGuestQuery> => {
    return {
      ...args?.options,
      queryKey: ['nodeListByGuest'],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            ${articleTeaserFragment}
            ${commentaryTeaserFragment}
            query NodeListByGuest(
              $limit: Int
              $offset: Int
              $urlAlias: String!
            ) {
              guestNodes: nodeListByGuest(
                limit: $limit
                offset: $offset
                urlAlias: $urlAlias
              ) {
                total
                items {
                  ... on NewsArticle {
                    ...ArticleTeaserFragment
                  }
                  ... on Commentary {
                    ...CommentaryTeaserFragment
                  }
                  ... on Sponsored {
                    title
                    teaserHeadline
                    id
                    urlAlias
                    createdAt
                    updatedAt
                    legacyThumbnailImageUrl
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  searchMetals: (
    args: QueryArgs<SearchMetalsQueryVariables, SearchMetalsQuery>,
  ): UseQueryOptions<SearchMetalsQuery> => {
    return {
      ...args?.options,
      queryKey: ['searchMetals', args?.variables],
      queryFn: async () =>
        await graphs.searchPricesFetch(
          gql`
            ${metalFragment}
            ${metalQuoteFragment}
            query SearchMetals(
              $symbol: String!
              $currency: String!
              $timestamp: Int!
            ) {
              metalData: GetMetalQuoteV3(
                symbol: $symbol
                currency: $currency
                timestamp: $timestamp
              ) {
                ...MetalFragment
              }
              cryptoData: GetCryptoQuoteV3(
                symbol: $symbol
                currency: $currency
                timestamp: $timestamp
              ) {
                symbol
                currency
                results {
                  ask
                  bid
                  high
                  low
                  open
                  close
                  change
                  changePercentage
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  searchNews: (
    args: QueryArgs<SearchNewsQueryVariables, SearchNewsQuery>,
  ): UseQueryOptions<SearchNewsQuery> => {
    return {
      ...args?.options,
      queryKey: ['searchNews', args?.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query SearchNews(
              $query: String!
              $sort: String!
              $limit: Int
              $offset: Int
            ) {
              searchData: search(
                query: $query
                sort: $sort
                limit: $limit
                offset: $offset
              ) {
                total
                items {
                  excerpt
                  id
                  relevance
                  title
                  urlAlias
                  updatedAt
                  legacyThumbnailImageUrl
                  thumbnail
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  latestNewsSidebar: (
    args: QueryArgs<LatestNewsSidebarQueryVariables, LatestNewsSidebarQuery>,
  ): UseQueryOptions<LatestNewsSidebarQuery> => {
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['latestNewsSidebar'],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query latestNewsSidebar($limit: Int, $offset: Int) {
              queue: nodeListQueue(
                limit: $limit
                offset: $offset
                bundles: [NewsArticle, OffTheWire, Commentary]
                queueId: "latest_news"
              ) {
                items {
                  ... on Commentary {
                    id
                    title
                    category {
                      name
                      urlAlias
                    }
                    urlAlias
                    updatedAt
                    image {
                      detail {
                        default {
                          srcset
                        }
                      }
                    }
                    teaserImage {
                      detail {
                        default {
                          srcset
                        }
                      }
                    }
                    legacyThumbnailImageUrl
                  }
                  ... on NewsArticle {
                    id
                    title
                    category {
                      name
                      urlAlias
                    }
                    urlAlias
                    updatedAt
                    image {
                      detail {
                        default {
                          srcset
                        }
                      }
                    }
                    teaserImage {
                      detail {
                        default {
                          srcset
                        }
                      }
                    }
                    legacyThumbnailImageUrl
                  }
                  ... on OffTheWire {
                    id
                    title
                    category {
                      name
                      urlAlias
                    }
                    urlAlias
                    updatedAt
                    image {
                      detail {
                        default {
                          srcset
                        }
                      }
                    }
                    teaserImage {
                      detail {
                        default {
                          srcset
                        }
                      }
                    }
                    legacyThumbnailImageUrl
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },
}
