import Head from 'next/head'
import type React from 'react'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'

interface Props {
  children?: React.ReactNode
  title: string
}

const LayoutLanding = ({ children, title }: Props) => {
  return (
    <>
      <Head>
        <title>{`${title} | KITCO`}</title>
      </Head>
      <main>
        <ErrBoundary>{children}</ErrBoundary>
      </main>
    </>
  )
}
export default LayoutLanding
