import clsx from 'clsx'

type SectionProps = {
  id: string
  title: string
  className: string
}

export default ({ id, title, className }: SectionProps): JSX.Element => {
  return (
    <p id={id} className={clsx(className, '!pt-8')}>
      <span
        lang="EN-US"
        style={{
          fontSize: '16.0pt',
          fontFamily: '"Lato",sans-serif',
          color: '#373737',
        }}
      >
        {title.toUpperCase()}
      </span>
    </p>
  )
}
