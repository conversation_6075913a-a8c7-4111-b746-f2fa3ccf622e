import { type QueryClient, useQuery } from '@tanstack/react-query'
import objectHash from 'object-hash'
import { useEffect, useMemo, useState } from 'react'
import type { MiningEquitiesTableQuery } from '~/src/generated'
import { MiningEquitiesQueries } from '~/src/lib/MiningEquities/Queries'
import type MiningEquity from '~/src/types/DataTable/MiningEquity'
import { formatPercentage } from '~/src/utils/Prices/formatPercentage'
import { formatPrice } from '~/src/utils/Prices/formatPrice'

/**
 * Get the Mining Equities data
 *
 * @returns {MiningEquity[]}
 */
const useMiningEquitiesData = (): MiningEquity[] => {
  // Get the data from the DB
  const { data } = useQuery(MiningEquitiesQueries.miningEquitiesTable())

  // Store the cached data to prevent re-rendering
  const [cachedData, setCachedData] = useState<MiningEquitiesTableQuery>(
    data ?? {},
  )

  // Only update the cached data if the data has changed
  useEffect(() => {
    if (!data) return

    if (objectHash(data) !== objectHash(cachedData)) {
      setCachedData(data)
    }
  }, [data])

  // Memoize the formatted data
  return useMemo(() => {
    return createTableData(cachedData)
  }, [cachedData])
}

/**
 * Get the Mining Equities data for SSR
 *
 * @returns {MiningEquity[]}
 */
const useMiningEquitiesDataSSR = async (
  queryClient: QueryClient,
): Promise<MiningEquity[]> => {
  const data = await queryClient.fetchQuery(
    MiningEquitiesQueries.miningEquitiesTable(),
  )

  return createTableData(data)
}

/**
 * Create the table data
 *
 * @param {MiningEquitiesTableQuery} data
 * @returns {MiningEquity[]}
 */
function createTableData(data: MiningEquitiesTableQuery): MiningEquity[] {
  return formatData(data)
}

/**
 * Get the top mining equities
 *
 * @param {MiningEquity[]} data
 * @param {string} category
 * @param {number} limit
 * @returns {MiningEquity[]}
 */
export function getTopMiningEquities(
  data: MiningEquity[],
  category = 'ALL',
  limit = 5,
): MiningEquity[] {
  return data
    .filter((item) => category === 'ALL' || item.Category.includes(category))
    .sort((a, b) => b.ChangePercentageVal - a.ChangePercentageVal)
    .slice(0, limit)
}

/**
 * Formats an array of MiningEquitiesTableQuery objects by categorizing and
 * formatting the values.
 *
 * @param {MiningEquitiesTableQuery} data - The array of Mining Data objects to format.
 * @returns {MiningEquity[]} - A new array of CommodityData objects with formatted values.
 */
function formatData(data: MiningEquitiesTableQuery): MiningEquity[] {
  // Return an empty array if there is no data
  if (!data || !data.GetEquities || data?.GetEquities.length <= 0) return []

  return data?.GetEquities.map((item) => {
    return {
      Category: item?.Category ?? '',
      Change: formatPrice({ value: item?.Change }) ?? '0.00',
      ChangePercentage:
        formatPercentage({ value: item?.ChangePercentage }) ?? '0.00',
      ChangePercentageVal: item?.ChangePercentage ?? 0,
      ChangeVal: item?.Change ?? 0,
      Currency: item?.Currency ?? '',
      Exchange: item?.Exchange ?? '',
      High: formatPrice({ value: item?.High }) ?? '0.00',
      HighVal: item?.High ?? 0,
      ID: item?.ID,
      Low: formatPrice({ value: item?.Low }) ?? '0.00',
      LowVal: item?.Low ?? 0,
      Name: item?.Name ?? '',
      Price: formatPrice({ value: item?.Price, min: 2 }) ?? '0.00',
      PriceVal: item?.Price ?? 0,
      Symbol: item?.Symbol,
      SymbolWithPrefix:
        item?.Exchange && item?.Symbol
          ? `${item?.Exchange}:${item?.Symbol}`
          : '',
      SymbolURL: item?.SymbolURL,
      TVExchange: item?.TVExchange ?? '',
      TVSymbol: item?.TVSymbol ?? '',
      Timestamp: item?.Timestamp,
      Volume: formatPrice({ value: item?.Volume, min: 0 }) ?? '0.00',
      VolumeVal: item?.Volume ?? 0,
    }
  })
}

export { createTableData, useMiningEquitiesData, useMiningEquitiesDataSSR }
