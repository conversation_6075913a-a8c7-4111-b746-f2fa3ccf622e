import Head from 'next/head'
import { useRouter } from 'next/router'
import type { FC } from 'react'

/**
 * NewsMeta Props
 *
 * @param {string} authorTwitter - The Twitter handle of the author
 * @param {string} description - The description of the page
 * @param {string} image - The image of the page
 * @param {string} title - The title of the page
 * @param {string} urlAlias - The URL alias of the page
 */
interface NewsMetaProps {
  authorTwitter?: string
  description?: string
  image?: string
  title?: string
  urlAlias?: string
}

/**
 * NewsMeta Component
 *
 * @param {NewsMetaProps} props - The props for the NewsMeta component
 * @returns {React.ReactElement} The rendered NewsMeta component
 */
const NewsMeta: FC<NewsMetaProps> = ({
  authorTwitter,
  description,
  image,
  title,
  urlAlias,
}: NewsMetaProps) => {
  // Use the useRouter hook to get the current path
  const { asPath } = useRouter()

  // Fallback values for title, description, and image
  const defaultTitle =
    'Latest News, Video News, Analysis and Opinions | KITCO NEWS'

  const defaultDescription =
    'The Kitco News Team brings you the latest news, videos, analysis and opinions regarding Precious Metals, Crypto, Mining, World Markets and Global Economy.'

  const defaultImage = '/fallbacks/ktc_img_fallback_lg.jpg'

  return (
    <Head>
      <meta
        name="description"
        content={description ? description : defaultDescription}
      />
      {urlAlias && (
        <link rel="canonical" href={process.env.NEXT_PUBLIC_URL + urlAlias} />
      )}
      <meta
        name="keywords"
        content="Kitco news,Gold,Silver,Platinum,Palladium,PGM,Platinum Group Metals,Metals,Precious Metals,Mining News,Crypto News,Bitcoin News,Ethereum News,Mining News,Interviews,Economic Reports,Forecasts,Central Banks,US Dollar,Charts,Tech Metals,Rare Earth Metals,Currency,Global Economy,International Policy,Politics,Bank Forecasts,Market Nugget,Mining Minutes,Roundups"
      />
      <meta property="og:url" content={asPath} />
      <meta property="og:type" content="article" />
      <meta property="og:title" content={title ?? defaultTitle} />
      <meta
        property="og:description"
        content={description ?? defaultDescription}
      />
      <meta property="og:image" content={image ?? defaultImage} />
      <meta name="twitter:title" content={title ?? defaultTitle} />
      <meta name="twitter:card" content="summary" />
      <meta name="twitter:site" content="@KitcoNewsNOW" />
      {authorTwitter && (
        <meta name="twitter:creator" content={`@${authorTwitter}`} />
      )}
    </Head>
  )
}

export default NewsMeta
