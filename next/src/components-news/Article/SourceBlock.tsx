import type { FC } from 'react'

export const SourceBlock: FC<{
  name: string
  description: string
  subtitle: string
}> = ({ name, description, subtitle }) => (
  <div className="flex flex-col items-start border border-gray-300 bg-[#f8f8f8] p-2.5 text-base leading-5">
    <h5>
      <div className="font-bold">{name}</div>
      <div className="font-normal">{subtitle}</div>
    </h5>
    <div
      className="pt-2 text-xs font-normal text-ktc-desc-gray "
      dangerouslySetInnerHTML={{ __html: description }}
    />
  </div>
)
