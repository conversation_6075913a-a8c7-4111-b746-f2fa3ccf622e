import { type FC, useEffect, useState } from 'react'
import styles from '~/src/components-metals/ShanghaiGold/ShanghaiGold.module.scss'
import useWeight from '~/src/hooks/Weight/useWeight'
import WeightType from '~/src/types/WeightSelect/WeightType'
import { renderFn } from '~/src/utils/SangHai/priceConversion'
import cs from '~/src/utils/cs'

/**
 * ShangHaiContentRow props
 */
interface ShangHaiContentRowProps {
  isFetching: boolean
  data: any
}

const ShangHaiContentRow: FC<ShangHaiContentRowProps> = ({
  isFetching,
  data,
}: ShangHaiContentRowProps) => {
  // Get store from atom
  const weight = useWeight(WeightType.PreciousMetals, 'GRAM', 'shanghai')

  const formatOptions = { defaultWeight: 'GRAM' }

  // Initialize priceAM and pricePM
  const [priceAM, SetPriceAM] = useState('')
  const [pricePM, SetPricePM] = useState('')

  // Update priceAM and pricePM when results change
  useEffect(() => {
    if (data && weight) {
      SetPriceAM(renderFn(weight, data.am, formatOptions))
      SetPricePM(renderFn(weight, data.pm, formatOptions))
    }
  }, [data, weight])

  return (
    <>
      <div className={styles.gridTwoColumn}>
        <div className={styles.amOrPm}>
          <span>AM</span>
        </div>
        <div
          className={cs([
            styles.price,
            !isFetching ? 'undefined' : 'opacity-50',
          ])}
        >
          {priceAM}
        </div>
      </div>
      <div className={styles.gridTwoColumn}>
        <div className={styles.amOrPm}>
          <span>PM</span>
        </div>
        <div className={styles.price}>
          <span>{pricePM}</span>
        </div>
      </div>
    </>
  )
}

export default ShangHaiContentRow
