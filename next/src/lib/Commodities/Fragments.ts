import { gql } from 'graphql-request'

export const commoditiesNewsSidebarCategoryFragment = gql`
  fragment CommoditiesNewsSidebarCategoryFragment on Category {
    id
    name
    urlAlias
  }
`

export const commoditiesNewsSidebarImageFragment = gql`
  fragment CommoditiesNewsSidebarImageFragment on Image {
    detail {
      default {
        srcset
      }
      sources {
        teaser_small {
          srcset
          media
        }
        teaser_medium {
          srcset
          media
        }
        desktop {
          media
          srcset
        }
        mobile {
          media
          srcset
        }
        tablet {
          media
          srcset
        }
      }
    }
  }
`

export const commoditiesNewsSidebarArticleTeaserFragment = gql`
  fragment CommoditiesNewsSidebarArticleTeaserFragment on NewsArticle {
    id
    category {
      ...CommoditiesNewsSidebarCategoryFragment
    }
    teaserSnippet
    title
    teaserHeadline
    urlAlias
    createdAt
    image {
      ...CommoditiesNewsSidebarImageFragment
    }
  }
  ${commoditiesNewsSidebarCategoryFragment}
  ${commoditiesNewsSidebarImageFragment}
`
