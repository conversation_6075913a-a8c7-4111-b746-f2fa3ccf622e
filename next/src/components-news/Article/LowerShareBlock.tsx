import type { FC } from 'react'
import { listAuthorStr } from '~/src/components-news/Article/ListAuthorStr'
import CommentDrawer from '~/src/components/Comment/CommentDrawer'
import { SocialsKitco } from '~/src/components/socials/socials-kitco.component'
import type { NewsArticle } from '~/src/generated'

export const LowerShareBlock: FC<{ articleData: NewsArticle }> = ({
  articleData,
}) => {
  return (
    <div className="flex w-full flex-col items-center gap-3 md:hidden">
      <h3 className="text-[16px] font-bold underline">Share</h3>
      <div className="flex flex-row gap-4">
        <SocialsKitco
          className="gap-4"
          listAuthorStr={listAuthorStr(articleData)}
        />
        <CommentDrawer
          className="text-kitco-black"
          category="news"
          storyID={articleData.id}
          elementID="ArticleCommentMobile"
        />
      </div>
    </div>
  )
}
