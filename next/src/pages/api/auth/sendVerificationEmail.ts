import type { messages } from 'mailgun-js'
import type { NextApiRequest, NextApiResponse } from 'next'
import admin from '~/src/services/firebase/admin/config'
import { mg } from '~/src/services/mailgun/mailgun'

type Data = {
  message: string
}

/**
 * Send a verification email to the user using Mailgun.
 *
 * @param req
 * @param res
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<Data>,
) {
  const { email, url } = req.body

  try {
    // Generate email verification link
    const verifyLink = await admin.auth().generateEmailVerificationLink(email, {
      url: url ?? process.env.NEXT_PUBLIC_URL,
    })

    // Get the user record
    const userRecord = await admin.auth().getUserByEmail(email)

    // Get the user's display name
    const displayName = userRecord.displayName || 'Kitco User'

    // Configure the email data
    const data:
      | messages.SendData
      | messages.BatchData
      | messages.SendTemplateData = {
      from: 'Kitco Forum <<EMAIL>>',
      to: email,
      subject: 'Kitco Forum - Verify Your Email',

      // Use the email template
      template: 'verify your email for kitco',

      // Add the email variables
      'h:X-Mailgun-Variables': JSON.stringify({
        verify_link: verifyLink,
        display_name: displayName,
      }),
    }

    // Send the email using Mailgun
    await mg.messages().send(data)

    res.status(200).json({ message: 'Verification email sent successfully.' })
  } catch (error) {
    console.error(error, email, url)
    res.status(500).json({ message: (error as Error).message })
  }
}
