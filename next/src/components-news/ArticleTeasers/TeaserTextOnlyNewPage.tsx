import Link from 'next/link'
import type { FC } from 'react'
import { CategoryLink } from '~/src/components-news/ArticleTeasers/CategoryLink'
import { DateStamp } from '~/src/components-news/ArticleTeasers/DateStamp'
import type { TeaserTextOnlyProps } from '~/src/components-news/ArticleTeasers/TeaserTextOnly'
import { Spacer } from '~/src/components/spacer/spacer.component'
import cs from '~/src/utils/cs'

export const TeaserTextOnlyNewPage: FC<TeaserTextOnlyProps> = ({
  node,
  size,
  hideCategory,
  hideSummary,
}) => {
  const sizeCSS = {
    sm: 'text-[16px] leading-[130%]',
    md: 'text-[20px] leading-[130%]',
    lg: 'text-[24px] leading-[130%]',
    xl: 'text-[24px] leading-[130%]',
  }

  return node ? (
    <div className="w-full">
      {!hideCategory && (
        <>
          <CategoryLink
            urlAlias={node?.category?.urlAlias}
            text={node?.category?.name}
          />
          <div className="h-1 bg-transparent" />
        </>
      )}
      <Link href={node?.urlAlias ?? '/'}>
        <>
          <h3
            className={cs([
              sizeCSS[size],
              'line-clamp-2 text-[20px] md:min-h-[44px] md:text-[17px]',
            ])}
          >
            {node?.teaserHeadline ?? node?.title}
          </h3>
          {!hideSummary ? (
            <>
              <Spacer className="h-2" />
              <div
                className="summary"
                dangerouslySetInnerHTML={{ __html: node?.teaserSnippet }}
              />
            </>
          ) : null}
          <Spacer className="h-2" />
          <DateStamp stamp={node?.createdAt} />
        </>
      </Link>
    </div>
  ) : null
}
