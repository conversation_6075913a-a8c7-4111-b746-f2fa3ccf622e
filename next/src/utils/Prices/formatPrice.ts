import getSymbolFromCurrency from 'currency-symbol-map'

interface FormatPriceParams {
  value: number
  category?: string
  min?: number
  max?: number
  currency?: string
  locales?: string
}

/**
 * Format the number based on the category
 *
 * @param {object} params - The parameters for formatting
 * @param {number} params.value - The value to format
 * @param {string} [params.category='default'] - The category of the commodity
 * @param {number} [params.min=0] - The minimum fraction digits
 * @param {number} [params.max=2] - The maximum fraction digits
 * @param {string} [params.currency] - The currency to use
 * @param {string} [params.locales='en-US'] - The locales to use
 * @returns {string} The formatted price
 */
export function formatPrice({
  value,
  category = 'default',
  min = 2,
  max = 2,
  currency = null,
  locales = 'en-US',
}: FormatPriceParams): string {
  let options: Intl.NumberFormatOptions = {
    minimumFractionDigits: min,
    maximumFractionDigits: max,
  }

  switch (category) {
    case 'PRECIOUS METALS':
      options = { minimumFractionDigits: 2, maximumFractionDigits: 2 }
      break
    case 'BASE METALS':
      options = { minimumFractionDigits: 4, maximumFractionDigits: 4 }
      break
    case 'ENERGY':
      options = { minimumFractionDigits: 2, maximumFractionDigits: 2 }
      break
    default:
  }

  // Convert value to positive (to avoid negative sign)
  value = Math.abs(value)

  // Get the formatted value to the correct locale
  const formattedValue = value.toLocaleString(locales, options)

  // Adjust the format to use the correct currency symbol
  if (currency) {
    const currencySymbol = getSymbolFromCurrency(currency)

    return `${currencySymbol}${formattedValue}`
  }

  return formattedValue
}
