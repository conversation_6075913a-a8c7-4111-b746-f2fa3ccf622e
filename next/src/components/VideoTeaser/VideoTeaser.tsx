import clsx from 'clsx'
import dynamic from 'next/dynamic'
import Image from 'next/image'
import Link from 'next/link'
import React, { type FC } from 'react'
import { teaserTimestampFromUTC } from '~/src/utils/teaser-timestamp'
import { ImageMS } from '../ImageMS/ImageMS.component'

type TVideoTeaserCtx = { node: any; isFetching?: boolean }
type guest = { id: number; name: string }
const VideoTeaserCtx = React.createContext<TVideoTeaserCtx>(null)
export const useVideoTeaserCtx = () => React.useContext(VideoTeaserCtx)

type BaseProps = { children?: React.ReactNode; className?: string }
type ProviderProps = BaseProps & {
  node: any
  isFetching?: boolean
}

const CtxProvider: FC<ProviderProps> = ({ children, node, isFetching }) => {
  if (!node && !isFetching) {
    return <p className="text-2xl font-medium">No result found</p>
  }

  return (
    <VideoTeaserCtx.Provider value={{ node, isFetching }}>
      {children}
    </VideoTeaserCtx.Provider>
  )
}

const Img: FC<{
  width: number
  height: number
  className?: string
  aspectRatio?:
    | 'aspect-video'
    | 'aspect-square'
    | 'aspect-auto'
    | 'aspect-[4/3]'
}> = (props) => {
  const { node, isFetching } = useVideoTeaserCtx()

  if (isFetching) {
    return <div className="animate-loading aspect-video w-full bg-black/10" />
  }

  return (
    <ImageMS
      src={`${node?.uuid}/${node?.thumbnailUuid}.jpeg`}
      alt={`${node?.headline} teaser image`}
      priority={true}
      width={props.width ?? 400}
      height={props.height ?? 340}
      service="vcms"
      className={clsx(
        'w-full',
        'relative',
        'rounded-md',
        props.className,
        props?.aspectRatio ?? 'aspect-video',
      )}
    />
  )
}

const PlayButton: FC<BaseProps> = ({ className }) => {
  return (
    <div className={clsx('absolute inset-0 flex items-center justify-center')}>
      <Image
        width={30}
        height={30}
        src={'/bi_play-circle-fill.svg'}
        alt="circle-play-icon"
        className={className}
      />
    </div>
  )
}

const A: FC<BaseProps> = ({ children, className }) => {
  const { node } = useVideoTeaserCtx()
  return (
    <Link
      href={node?.urlAlias || '/error'}
      className={clsx('relative block', className)}
    >
      {children}
    </Link>
  )
}

const Category: FC<BaseProps> = ({ className }) => {
  const { node, isFetching } = useVideoTeaserCtx()

  if (!node?.categories?.length) return null

  return (
    <div className="block text-wrap overflow-hidden break-all">
      {node?.categories?.map((category) => (
        <Link
          key={category.urlAlias}
          className={clsx(
            isFetching && 'animate-loading h-[20px] w-[100px]',
            'text-[11px] font-extrabold uppercase leading-3 tracking-wider text-ktc-category',
            'relative mt-2 pr-2 break-all',
            !className ? null : className,
          )}
          href={
            category.urlAlias ? `/news/video${category.urlAlias}` : '/error'
          }
        >
          {category.name}
        </Link>
      ))}
    </div>
  )
}

// for loading states, explicitly pass in tailwind dimensions to create dimensions
const Title: FC<BaseProps> = ({ className }) => {
  const { node, isFetching } = useVideoTeaserCtx()

  return (
    <h3
      className={clsx(
        isFetching && 'animate-loading h-[20px] min-w-[200px]',
        'text-[13px] text-base font-bold leading-[130%]',
        'my-[5px] line-clamp-3 overflow-hidden overflow-ellipsis',
        className,
      )}
    >
      {node?.headline}
    </h3>
  )
}

const Timestamp: FC<BaseProps> = ({ className }) => {
  const { node } = useVideoTeaserCtx()

  return (
    <time
      className={clsx('text-xs opacity-60', className)}
      dateTime={teaserTimestampFromUTC(node?.publishedAt ?? node?.updatedAt)}
    >
      {teaserTimestampFromUTC(node?.publishedAt ?? node?.updatedAt)}
    </time>
  )
}

const Description: FC<BaseProps> = ({ className }) => {
  const { node, isFetching } = useVideoTeaserCtx()

  return (
    <p
      className={clsx(
        isFetching && 'animate-loading h-[40px] w-[260px]',
        'leading-[142%]',
        className,
      )}
    >
      {node?.description}
    </p>
  )
}

const Source: FC = () => {
  const { node } = useVideoTeaserCtx()

  return (
    <h5 className="!font-normal !text-white/50">
      Source: <span className="!text-white/70">{node?.source}</span>
    </h5>
  )
}

const Guests: FC = () => {
  const { node } = useVideoTeaserCtx()

  return (
    <h5 className="!font-normal !text-white/50">
      Guests:{' '}
      <span className="!text-white/70">
        {node?.guests?.map((guest: guest) => guest.name).join(', ')}
      </span>
    </h5>
  )
}

const Player = dynamic(() => import('./VideoPlayerWrapper'), {
  ssr: false,
})

export {
  A,
  Category,
  CtxProvider,
  Description,
  Guests,
  Img,
  PlayButton,
  Player,
  Source,
  Timestamp,
  Title,
}
