import { type FC, Suspense } from 'react'
import { TeaserTextOnly } from '~/src/components-news/ArticleTeasers/TeaserTextOnly'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import { Query } from '~/src/components/Query/Query'
import type { ArticleTeaserFragmentFragment } from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'

const LatestNewsSection: FC<{ hideLatestNewsHeader?: boolean }> = ({
  hideLatestNewsHeader,
}) => {
  const variables = hideLatestNewsHeader
    ? {
        urlAlias: '/news/category/mining',
        limit: 5,
        offset: 0,
        includeEntityQueues: false,
        includeRelatedCategories: false,
      }
    : {
        urlAlias: '/news/category/commodities',
        limit: 5,
        offset: 0,
      }

  const fetcher = news.newsByCategoryGeneric({
    variables: variables,
    options: {
      enabled: true,
    },
  })

  return (
    <ErrBoundary>
      <Suspense fallback={<div>Loading...</div>}>
        <Query fetcher={fetcher}>
          {({ data }) => {
            return (
              <div className="flex flex-col">
                {!hideLatestNewsHeader && (
                  <h2 className="border-b border-ktc-borders pb-2.5 text-[20px] uppercase">
                    <span>Latest News</span>
                  </h2>
                )}
                <div className="flex flex-grow flex-col">
                  {data?.nodeListByCategory?.items
                    ?.slice(0, 5)
                    .map((x: ArticleTeaserFragmentFragment) => {
                      return (
                        <div className="mt-5 flex" key={x.id}>
                          <TeaserTextOnly
                            key={x?.id}
                            node={x}
                            hideSummary={true}
                            size={'sm'}
                          />
                        </div>
                      )
                    })}
                </div>
              </div>
            )
          }}
        </Query>
      </Suspense>
    </ErrBoundary>
  )
}

export default LatestNewsSection
