#!/usr/bin/env bash

# Default values
DEFAULT_CONTEXT="docker-desktop"
DEFAULT_NAMESPACE="default-namespace"

# Parse command-line arguments
while getopts c:n:a:r: flag
do
    case "${flag}" in
        c) CONTEXT_NAME=${OPTARG};;
        n) NAMESPACE=${OPTARG};;
        a) ACTION=${OPTARG};;
        *)
          echo "Usage: $0 [-c context_name] [-n namespace] [-a action]"
          exit 1
          ;;
    esac
done

# Source the custom Helm deploy script first to set DEFAULT_NAMESPACE
source ./custom_helm_deploy.sh || { echo "Failed to load custom Helm deploy script"; exit 1; }

# Use default values if not set by arguments, including updated DEFAULT_NAMESPACE
CONTEXT_NAME=${CONTEXT_NAME:-$DEFAULT_CONTEXT}
NAMESPACE=${NAMESPACE:-$DEFAULT_NAMESPACE}

# Debugging step to check if context_namespace.sh is sourced correctly
echo "Sourcing context_namespace.sh with CONTEXT_NAME=${CONTEXT_NAME} and NAMESPACE=${NAMESPACE}"
source ./context_namespace.sh -c "$CONTEXT_NAME" -n "$NAMESPACE" || { echo "Failed to load context and namespace"; exit 1; }

case "${ACTION}" in
    deploy)
        echo "Deploying ${CONTEXT_NAME}/${NAMESPACE} via custom helm deployment script"
        deploy_helm "$NAMESPACE" "$DIR"
        ;;

    template)
        echo "Generating Helm template for ${CONTEXT_NAME}/${NAMESPACE} via custom helm deployment script"
        template_helm "$NAMESPACE" "$DIR"
        ;;

    remove)
        echo "Deleting deployment of ${CONTEXT_NAME}/${NAMESPACE}..."
        helm uninstall "$NAMESPACE" --namespace "$NAMESPACE" || { echo "Failed to delete deployment."; }

        echo -n "Do you want to delete the namespace '${NAMESPACE}'? This will remove all resources within it. (y/n) "
        read -r delete_ns
        if [ "$delete_ns" != "${delete_ns#[Yy]}" ]; then
          echo "Deleting namespace '${NAMESPACE}'..."
          kubectl delete namespace "$NAMESPACE" || { echo "Failed to delete namespace."; exit 1; }
        else
          echo "Namespace '${NAMESPACE}' will be preserved."
        fi
        ;;

    pause)
        echo "Scaling down all deployments in namespace ${NAMESPACE} to zero replicas..."
        DEPLOYMENTS=$(kubectl get deployments --namespace "$NAMESPACE" --no-headers | wc -l)
        if [ "$DEPLOYMENTS" -gt 0 ]; then
            kubectl scale --namespace "$NAMESPACE" --replicas=0 deployment --all || { echo "Failed to scale down deployments."; exit 1; }
            kubectl scale --namespace "$NAMESPACE" --replicas=0 statefulset --all || { echo "Failed to scale down statefulsets."; exit 1; }
            echo "All deployments and statefulsets in namespace ${NAMESPACE} have been scaled down to zero replicas."
        else
            echo "No deployments found in namespace ${NAMESPACE} to scale down."
        fi
        ;;

    unpause)
        echo "Re-deploying ${CONTEXT_NAME}/${NAMESPACE} to restore the replicas defined in Helm values file"
        deploy_helm "$NAMESPACE" "$DIR"
        ;;

    *)
        echo "Invalid action specified. Use 'deploy', 'template', 'remove', 'pause', or 'unpause'."
        exit 1
        ;;
esac

echo "Process completed."
