import type { FC } from 'react'
import ShangHaiContentRow from '~/src/components-metals/ShanghaiGold/ShangHaiContentRow'

/**
 * ShangHaiContent props
 */
interface ShangHaiContentProps {
  conversionRate?: number
  data: any
  isLoading: boolean
  symbol: string
  timeStamp: number
}

/**
 * ShangHaiContent component
 */
const ShangHaiContent: FC<ShangHaiContentProps> = ({
  conversionRate,
  data,
  isLoading,
  symbol,
}: ShangHaiContentProps) => {
  if (!data) {
    // Handle case where result is not available
    return <div>Error, no ShangHai Data</div>
  }

  // If symbol is CNY, render ShangHaiContentRow without conversion
  if (symbol === 'CNY') {
    return <ShangHaiContentRow isFetching={isLoading} data={data} />
  }

  // If conversionRate is not provided, render ShangHaiContentRow with conversion
  const dataConverted = {
    ...data,
    am: data.am * conversionRate,
    pm: data.pm * conversionRate,
  }

  return <ShangHaiContentRow isFetching={isLoading} data={dataConverted} />
}

export default ShangHaiContent
