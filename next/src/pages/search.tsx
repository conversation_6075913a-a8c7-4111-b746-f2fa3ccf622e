import clsx from 'clsx'
import Link from 'next/link'
import { useRouter } from 'next/router'
import type React from 'react'
import type { ReactNode } from 'react'
import { type FC, useEffect, useState } from 'react'
import { CgSpinner } from 'react-icons/cg'
import { IoClose } from 'react-icons/io5'
import { PiMagnifyingGlassBold } from 'react-icons/pi'
import Layout from '~/src/components/Layout/Layout'
import Table from '~/src/components/Table/Table'
import { colorize } from '~/src/utils/colorize-change.util'
import { kitcoCryptos } from '~/src/utils/kitcoDatas'
import priceFormatter from '~/src/utils/priceFormatter'
import { teaserTimestamp } from '~/src/utils/teaser-timestamp'
import { ImageMS } from '../components/ImageMS/ImageMS.component'
import {
  type SearchCriteria,
  useSearchStateAndEvents,
} from '../utils/use-search.hook'

// const SORTS = {
//   Relevance: "Relevance",
//   Date: "Date",
// };

// const LoadMoreButton: FC<{ onClick: () => void; loading: boolean }> = ({
//   onClick,
// }) => {
//   return (
//     <>
//       <div className="pb-10 md:py-9">
//         <button
//           className={clsx(
//             'flex w-full items-center justify-center gap-2 rounded-md border border-ktc-borders py-2 text-base',
//           )}
//           type="button"
//           onClick={onClick}
//         >
//           <span className="font-medium leading-5 text-[#111111]">
//             Load More
//           </span>
//           <IoChevronDownOutline />
//         </button>
//       </div>
//     </>
//   )
// }

export default function SearchPage(): ReactNode {
  const r = useRouter()
  const { state, handlers } = useSearchStateAndEvents()
  const [inputValue, setInputValue] = useState(r.query.term)
  const [keywordEntered, setKeywordEntered] = useState(false)
  const [isBottom, setIsBottom] = useState(false)
  // const limitAutoLoad = 90

  useEffect(() => {
    if (r.query.term) setInputValue(r.query.term)
  }, [r.query.term])

  const handleChangeInput = (event) => {
    setInputValue(event.target.value)
    setKeywordEntered(!!event.target.value)
  }

  const handleClearInput = () => {
    handlers.onSearchClear()
    setInputValue('')
    setKeywordEntered(false)
  }

  // commented out handleFilter while Relevance is not working
  // const handleFilter = (e) => {
  //   handlers.onSortSelected(e);
  // };

  // const renderDivRef = () => {
  //   return state.params.offset <= limitAutoLoad && <div ref={state.ref}></div>
  // }

  const handleScroll = () => {
    const scrollTop =
      (document.documentElement && document.documentElement.scrollTop) ||
      document.body.scrollTop
    const scrollHeight =
      (document.documentElement && document.documentElement.scrollHeight) ||
      document.body.scrollHeight
    const clientHeight =
      document.documentElement.clientHeight || window.innerHeight
    const scrolledToBottom =
      Math.ceil(scrollTop + clientHeight) > scrollHeight - 500
    setIsBottom(scrolledToBottom)
  }

  useEffect(() => {
    if (state.isFetching) return
    if (isBottom) {
      state.fetchMore()
    }
  }, [isBottom])

  useEffect(() => {
    window.addEventListener('scroll', handleScroll)
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  // const renderLoadMoreButton = () => {
  //   return (
  //     !state.isFetching &&
  //     state.params.offset > limitAutoLoad &&
  //     state.data.searchData?.items.length < state.data.searchData?.total && (
  //       <LoadMoreButton
  //         onClick={() => {
  //           state.fetchMore()
  //         }}
  //         loading={state.isFetching}
  //       />
  //     )
  //   )
  // }

  const renderSpinner = () => {
    return (
      state.isFetching && (
        <div className="flex h-64 items-center justify-center">
          <CgSpinner className="h-14 w-14 animate-spin rounded-full" />
        </div>
      )
    )
  }

  return (
    <Layout title="Search">
      <div className="mx-auto flex w-[48rem] max-w-full flex-col px-3">
        <form
          onSubmit={handlers.onSearchButtonClick}
          className={clsx('flex items-center', 'border-b border-ktc-borders')}
        >
          <input
            type="text"
            autoComplete="off"
            name="search"
            placeholder="Search..."
            value={inputValue}
            onChange={handleChangeInput}
            className="flex w-[calc(100%_-_46px_-_46px)] flex-grow items-center border-none font-['Bebas_Neue'] text-[32px] text-[#232323] outline-none md:text-5xl desktop:w-[calc(100%_-_50px_-_50px)]"
          />

          <button
            type="reset"
            onClick={handleClearInput}
            className={clsx(
              'ml-auto p-2 transition hover:text-blue-500',
              (state.criteria.inputTerm && inputValue) || keywordEntered
                ? 'block'
                : 'hidden',
            )}
          >
            <IoClose className="h-7 w-7 desktop:h-8 desktop:w-8" />
          </button>

          <button
            type="submit"
            className="ml-auto justify-self-end p-2 transition hover:text-blue-500"
          >
            <PiMagnifyingGlassBold className="h-7 w-7 desktop:h-8 desktop:w-8" />
          </button>
        </form>
        {state.criteria.inputTerm && (
          <div className="mb-4 mt-4 flex w-full flex-col-reverse justify-between gap-2 align-text-top md:flex-row">
            <CountResults
              total={state.data?.searchData?.total?.toLocaleString()}
              keyword={state.criteria.inputTerm}
            />
            {/* temporarily hide SortSelect while Relevance is not working */}
            {/* <SortSelect
              sortBy={state.criteria.sort}
              handleFilter={handleFilter}
            /> */}
          </div>
        )}
        {!state.criteria.inputTerm && !state.isFetching && (
          <EmptyResults>
            <span>Search for articles, metals, and pages.</span>
          </EmptyResults>
        )}
        <AsyncResults
          criteria={state.criteria}
          data={state.data}
          isFetching={state.isFetching}
        />
        {/* {renderDivRef()} */}
        {/* {renderLoadMoreButton()} */}
        {renderSpinner()}
      </div>
    </Layout>
  )
}

const CountResults: FC<{ total: string; keyword: string }> = ({
  total,
  keyword,
}) => {
  return (
    <div className="text-[15px] text-ktc-date-gray">
      About {total ?? 0} results for&nbsp;
      <span className="text-[19px] font-semibold text-kitco-black">
        {keyword}
      </span>
    </div>
  )
}

const EmptyResults = ({ children }: { children: React.ReactNode }) => (
  <div className="my-16 flex flex-col items-center">
    <h1 className="text-xl font-semibold text-gray-700">
      {children || 'No results found'}
    </h1>
  </div>
)

function AsyncResults({
  data,
  criteria,
  isFetching,
}: {
  data: ReturnType<typeof useSearchStateAndEvents>['state']['data']
  criteria: SearchCriteria
  isFetching: boolean
}) {
  return (
    <>
      {data?.metalData && (
        <div className="my-8">
          <CommodityItem commodity={data?.metalData} />
        </div>
      )}
      {data?.cryptoData && (
        <div className="my-8">
          <CryptoItem crypto={data?.cryptoData} />
        </div>
      )}
      <ul>
        {criteria.inputTerm && (
          <>
            {!data?.metalData &&
            !data?.cryptoData &&
            !data?.searchData?.items?.length &&
            !isFetching ? (
              <EmptyResults>
                <span>
                  {}
                  We couldn't find any results matching '{criteria.inputTerm}',
                  please try a different search term.
                </span>
              </EmptyResults>
            ) : (
              data?.searchData?.items?.map((x, index) => (
                <ResultItem
                  key={x.id}
                  title={x.title}
                  description={x.excerpt}
                  urlAlias={x.urlAlias}
                  index={index}
                  updatedAt={x.updatedAt}
                  legacyThumbnailImageUrl={x.legacyThumbnailImageUrl}
                  thumbnail={x.thumbnail}
                />
              ))
            )}
          </>
        )}
      </ul>
    </>
  )
}

const ResultItem = ({
  title,
  description,
  urlAlias,
  index,
  updatedAt,
  legacyThumbnailImageUrl,
  thumbnail,
}) => {
  const isFirstItem = index === 0
  return (
    <li
      className={`color-black my-2 flex py-4 ${
        isFirstItem ? '' : 'border-t'
      } border-ktc-date-gray/20`}
    >
      <Link className="text-black" href={urlAlias}>
        <ImageMS
          src={thumbnail}
          hasLegacyThumbnailImageUrl={!!legacyThumbnailImageUrl}
          alt={`${title} teaser image`}
          priority={true}
          width={304}
          height={170}
          service="icms"
          className={clsx(
            'relative hidden md:block md:!w-[152px] md:!min-w-[152px]',
            'aspect-video rounded-lg object-cover',
          )}
        />
      </Link>
      <div className="flex flex-col pl-0 md:pl-5">
        <Link className="text-black" href={urlAlias}>
          <span className="link-hover text-base font-bold transition">
            {title}
          </span>
        </Link>
        <div
          className="line-clamp-2"
          dangerouslySetInnerHTML={{ __html: description }}
        />
        <DateTime updatedAt={Number(updatedAt)} className="pt-2" />
      </div>
    </li>
  )
}

const CommodityItem = ({ commodity }) => {
  const prices = commodity.results[0]
  const priceClass = 'text-md font-semibold md:text-lg'

  return (
    <Table title={`${commodity.name} Price Now`}>
      <div className="grid grid-cols-3 border-b border-gray-200 px-6 py-2 sm:grid-cols-5">
        <h5>BID</h5>
        <h5>ASK</h5>
        <h5 className="hidden sm:block">HIGH</h5>
        <h5 className="hidden sm:block">LOW</h5>
        <h5>CHANGE</h5>
      </div>
      <div className="grid grid-cols-3 p-6 sm:grid-cols-5">
        <h3 className={priceClass}>{priceFormatter(prices.bid)}</h3>
        <h3 className={priceClass}>{priceFormatter(prices.ask)}</h3>
        <h3 className={clsx(priceClass, 'hidden sm:block')}>
          {priceFormatter(prices.high)}
        </h3>
        <h3 className={clsx(priceClass, 'hidden sm:block')}>
          {priceFormatter(prices.low)}
        </h3>
        <h3
          className={clsx(priceClass, colorize(commodity.results[0]?.change))}
        >
          {commodity.results[0].change.toFixed(2)} (
          {commodity.results[0].changePercentage.toFixed(2)}
          %)
        </h3>
      </div>
    </Table>
  )
}

const CryptoItem = ({ crypto }) => {
  const prices = crypto.results[0]
  const priceClass = 'text-lg font-semibold'

  return (
    <Table
      title={`${
        kitcoCryptos.find((x) => x.symbol === crypto.symbol)?.name
      } Price Now`}
    >
      <div className="grid grid-cols-3 border-b border-gray-200 px-6 py-2 sm:grid-cols-5">
        <h5>HIGH</h5>
        <h5>LOW</h5>
        <h5 className="hidden sm:block">OPEN</h5>
        <h5 className="hidden sm:block">CLOSE</h5>
        <h5>CHANGE</h5>
      </div>
      <div className="grid grid-cols-3 p-6 sm:grid-cols-5">
        <h3 className={priceClass}>{priceFormatter(prices.high)}</h3>
        <h3 className={priceClass}>{priceFormatter(prices.low)}</h3>
        <h3 className={clsx(priceClass, 'hidden sm:block')}>
          {priceFormatter(prices.open)}
        </h3>
        <h3 className={clsx(priceClass, 'hidden sm:block')}>
          {priceFormatter(prices.close)}
        </h3>
        <h3 className={clsx(priceClass, colorize(crypto.results[0]?.change))}>
          {crypto.results[0].change.toFixed(2)} (
          {crypto.results[0].changePercentage.toFixed(2)}
          %)
        </h3>
      </div>
    </Table>
  )
}

const DateTime: FC<{ updatedAt; className?: string }> = ({
  updatedAt,
  className,
}) => {
  return (
    <time
      dateTime={updatedAt}
      className={clsx('text-xs font-medium text-ktc-date-gray', className)}
    >
      {updatedAt && teaserTimestamp(updatedAt)}
    </time>
  )
}

// commented out SortSelect while Relevance is not working
// const SortSelect: FC<{ sortBy: string; handleFilter: (arg?: any) => void }> = ({
//   sortBy,
//   handleFilter,
// }) => {
//   return (
//     <div className="flex items-center shrink-0">
//       <span className="mr-3">Sort by</span>
//       <Listbox value={sortBy} onChange={handleFilter}>
//         <div
//           className={clsx(
//             "relative w-max inline-block",
//             "border border-ktc-gray/20 rounded-sm",
//           )}
//         >
//           <ListboxButton
//             className={clsx(
//               "px-2 h-7 w-full",
//               "flex items-center justify-between gap-1",
//             )}
//           >
//             <span>
//               <span className="block truncate">{sortBy}</span>
//             </span>
//             <span className={clsx("pl-1")}>
//               <IoChevronDownSharp />
//             </span>
//           </ListboxButton>
//           <Transition
//             as={Fragment}
//             leave="transition ease-in duration-100"
//             leaveFrom="opacity-100"
//             leaveTo="opacity-0"
//           >
//             <ListboxOptions
//               className={clsx(
//                 "z-[2000] absolute mt-1 h-auto py-1 ",
//                 "rounded-md bg-white min-w-max w-full",
//                 "text-base shadow-lg ring-1 ring-black/20 focus:outline-none sm:text-sm",
//               )}
//             >
//               {Object.values(SORTS).map((x, idx) => (
//                 <ListboxOption
//                   key={idx}
//                   className={({ active }) =>
//                     clsx(
//                       "block relative cursor-pointer select-none py-2 px-4",
//                       active ? "bg-blue-200/30 text-blue-800" : "text-gray-900",
//                     )
//                   }
//                   value={x}
//                 >
//                   <div className="flex items-center gap-2 flex-nowrap">
//                     <span className={clsx("block font-semibold basis-12")}>
//                       {x}
//                     </span>
//                   </div>
//                 </ListboxOption>
//               ))}
//             </ListboxOptions>
//           </Transition>
//         </div>
//       </Listbox>
//     </div>
//   );
// };
