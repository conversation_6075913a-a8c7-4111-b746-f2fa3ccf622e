import type { NextRequest } from 'next/server'

export const config = {
  runtime: 'edge',
}

const base =
  'https://ondemand.websol.barchart.com/getQuote.json?apikey=44f0395dd7f6659f4ccc43e2c43200fd&symbols='

export default async function handler(req: NextRequest) {
  const url = new URL(req.url)
  const symbols = url.pathname.replace('/api/getQuote/', '')

  try {
    const response = await fetch(`${base}${symbols}`)
    if (!response.ok) {
      throw new Error(`Failed to fetch data for symbols: ${symbols}`)
    }
    const data = await response.json()

    return new Response(JSON.stringify(data), {
      headers: { 'Content-Type': 'application/json' },
    })
  } catch (error) {
    console.error('Fetch error:', error)
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    })
  }
}
