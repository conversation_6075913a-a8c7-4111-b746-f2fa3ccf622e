import { metals } from '~/src/lib/metals-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import * as timestamps from '~/src/utils/timestamps'

/**
 * Get the "now" data query for the gold price
 *
 * @returns {MetalQuoteQuery} The "now" data query
 * @param {string} currency The currency to fetch the data for
 * @param {string} symbol The symbol to fetch the data for
 */
const getMetalNowDataQuery = (currency = 'USD', symbol = 'AU') => {
  // Create the query arguments
  return metals.metalQuote({
    variables: {
      currency: currency ?? 'USD',
      symbol: symbol ?? 'AU',
      timestamp: timestamps.current(),
    },
    options: {
      refetchOnWindowFocus: true,
    },
  })
}

/**
 * Fetches the "now" data for the gold price
 *
 * @returns {MetalQuoteQuery} The "now" data
 *
 * @param {string} currency The currency to fetch the data for
 * @param {string} symbol The symbol to fetch the data for
 */
const useMetalNowData = (currency = 'USD', symbol = 'AU') => {
  // Get the "now" data
  return kitcoQuery(getMetalNowDataQuery(currency, symbol))
}

export default useMetalNowData
