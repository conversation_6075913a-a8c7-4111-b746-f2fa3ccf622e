@import '~/src/styles/article.scss';

.articleBodyStyles {
  & p,
  & pre {
    margin: 1em 0;
  }

  & blockquote {
    & p {
      margin: 0;
    }
  }
}

.articleBulletNews {
  margin-top: -10px;

  font-size: 18px;
  line-height: 110%;

  &::before {
    content: '';
    border-color: transparent #111;
    border-style: solid;
    border-width: 0.35em 0 0.35em 0.45em;
    display: block;
    width: 5px;
    height: 10px;
    left: -1em;
    top: 0.9em;
    position: relative;
  }
}

.articleWrapper {
  @include articleWrapper;

  pre {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 400;
    font-size: 17px;
    line-height: 26px;
    color: #111111;
    white-space: pre-wrap;
  }
}
