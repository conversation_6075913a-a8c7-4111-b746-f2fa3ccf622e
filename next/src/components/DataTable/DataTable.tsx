import {
  type ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  type TableOptions,
  useReactTable,
} from '@tanstack/react-table'
import clsx from 'clsx'
import { useEffect, useMemo, useState } from 'react'
import { FaCaretDown, FaCaretUp, FaSort } from 'react-icons/fa'
import DataTablePagination from '~/src/components/DataTable/Pagination/DataTablePagination'
import { getCommonPinningStyles } from '~/src/components/DataTable/Styles/DataTablePinningStyles'

import { DraggableRow } from '~/src/components/GoldIndex/DataTable/DragHandle'

interface DataTableProps<T extends object> {
  data: T[]
  columns: ColumnDef<T, any>[]
  extraConfig?: Partial<TableOptions<T>>
  isLoading?: boolean
  scrollOnDesktop?: boolean
  paginationEnabled?: boolean
  paginationClassName?: string
  onReorder?: (newData: T[]) => void
  disableDragDrop?: boolean
  categories?: string[]
  activeCategory?: string
  onCategoryChange?: (category: string) => void
}

const DataTable = <T extends object>({
  data,
  columns,
  extraConfig,
  scrollOnDesktop = false,
  paginationEnabled = false,
  isLoading,
  paginationClassName,
  // onReorder,
  disableDragDrop = false,
  // categories = [
  //   'ALL',
  //   'GOLD',
  //   'SILVER',
  //   'PGM',
  //   'BASE METALS',
  //   'BATTERY MATERIALS',
  //   'ENERGY',
  //   'RARE EARTH'
  // ],
  // activeCategory = 'GOLD',
  // onCategoryChange = () => { },
}: DataTableProps<T>) => {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  const rowsWithIds = useMemo(() => {
    if (!data || data.length === 0) return []

    return data.map((item: any) => ({
      ...item,
      _rowId:
        item.commodity || item.id || item.Symbol || Math.random().toString(36),
    }))
  }, [data])

  const displayData = rowsWithIds

  const table = useReactTable({
    data: displayData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    ...(extraConfig || {}),
  })

  const [selectedRowId, setSelectedRowId] = useState<string | null>(null)

  const handleRowClick = (rowId: string) => {
    setSelectedRowId(rowId === selectedRowId ? null : rowId)
  }

  useEffect(() => {
    if (table.getState().pagination.pageSize) {
      table.setPageSize(table.getState().pagination.pageSize)
    }
  }, [extraConfig])

  if (isLoading) {
    return <div>Loading...</div>
  }

  return (
    <div
      className={clsx(
        'w-full overflow-x-auto',
        scrollOnDesktop ? '' : 'lg:overflow-x-hidden',
      )}
    >
      <table className={`table-auto w-full`}>
        <thead>
          {table.getHeaderGroups().map((headerGroup) => (
            <tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => {
                const { column } = header
                const headerMeta: any = header.column.columnDef.meta
                const headerClassName = headerMeta?.classNameHeader

                return (
                  <th
                    key={header.id}
                    colSpan={header.colSpan}
                    style={{ ...getCommonPinningStyles(column, true) }}
                    className={clsx(
                      // ' h-11 max-h-11 items-center justify-start self-stretch border-b border-slate-200 bg-neutral-100 px-2 pl-4 text-left',
                      'h-11 max-h-11 items-center justify-start self-stretch border-b border-slate-200 bg-neutral-100 px-2 pl-4 text-left',
                      headerClassName,
                    )}
                  >
                    <div
                      className={clsx(
                        'flex cursor-pointer select-none items-center gap-2 text-xs font-bold leading-tight text-zinc-600',
                        header.column.getCanSort()
                          ? 'cursor-pointer select-none'
                          : '',
                        headerMeta?.classNameHeaderDiv,
                      )}
                      onClick={header.column.getToggleSortingHandler()}
                      onKeyDown={header.column.getToggleSortingHandler()}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}{' '}
                      {header.column.getIsSorted() ? (
                        {
                          asc: <FaCaretUp />,
                          desc: <FaCaretDown />,
                        }[header.column.getIsSorted() as string]
                      ) : header.column.getCanSort() ? (
                        <FaSort />
                      ) : null}
                    </div>
                  </th>
                )
              })}
              {!disableDragDrop && (
                <th className="w-0 px-0 bg-transparent border-b-0 opacity-0 invisible" />
              )}
            </tr>
          ))}
        </thead>
        <tbody>
          {table.getRowModel().rows.map((row) =>
            !disableDragDrop && isClient ? (
              <DraggableRow key={row.id} id={(row.original as any)._rowId}>
                {row.getVisibleCells().map((cell) => {
                  const { column } = cell
                  const headerMeta: any = cell.column.columnDef.meta
                  const cellClassName = headerMeta?.classNameCell

                  return (
                    <td
                      key={cell.id}
                      style={{ ...getCommonPinningStyles(column, false) }}
                      className={clsx('px-4', cellClassName)}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </td>
                  )
                })}
              </DraggableRow>
            ) : (
              <tr
                key={row.id}
                className={clsx(
                  'border-b border-slate-200 hover:bg-[#F8F8F8]',
                  row.id === selectedRowId ? 'bg-[#EFEFEF]' : 'bg-white',
                )}
                onClick={() => handleRowClick(row.id)}
                onKeyDown={() => handleRowClick(row.id)}
              >
                {row.getVisibleCells().map((cell) => {
                  const { column } = cell
                  const headerMeta: any = cell.column.columnDef.meta
                  const cellClassName = headerMeta?.classNameCell

                  return (
                    <td
                      key={cell.id}
                      style={{ ...getCommonPinningStyles(column, false) }}
                      className={clsx('px-4', cellClassName)}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </td>
                  )
                })}
              </tr>
            ),
          )}
        </tbody>
      </table>
      {paginationEnabled && (
        <div className={paginationClassName}>
          <DataTablePagination
            table={table}
            setPageSize={table.setPageSize}
            setPageIndex={table.setPageIndex}
          />
        </div>
      )}
    </div>
  )
}

export default DataTable
