.tabletGridOrder {
  display: grid;
  gap: 1.25em;
  grid-template-columns: 1fr;
  grid-template-areas:
    'qq'
    'aa'
    'bb'
    'cc'
    'dd'
    'ee'
    'kk'
    'll';

  @media (min-width: 768px) {
    // display: grid;
    // grid-template-columns: 1fr 1fr;
    // grid-template-areas:
    //   "aa qq"
    //   "bb qq"
    //   "ii ii"
    //   "cc gg"
    //   "dd hh"
    //   "ee ee"
    //   "ll ll"
    //   "mm mm"
    //   "nn nn"
    //   "oo oo"
    //   "pp pp";
    display: block;
  }

  @media (min-width: 1270px) {
    display: grid;
    grid-template-columns: 200px minmax(0, 1fr) 300px;
    grid-template-areas: none;
  }

  // Have to switch a little later, as at 1270 there is not room for the center well to be 728px large - TC 7/26/2024
  @media (min-width: 1320px) {
    grid-template-columns: 200px minmax(728px, 1fr) 336px;
  }
}

.miningBannerContainer {
  display: none;

  @media (min-width: 1270px) {
    display: block;
  }
}
