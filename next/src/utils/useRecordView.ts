import { useEffect } from 'react'

// Check if the code is running on the client side
const IS_CLIENT = typeof window !== 'undefined'

/**
 * Record view of the article using useEffect
 *
 * @param execute - A boolean value to determine if the record view should be executed
 * @param id - The id of the article
 */
export default function useRecordView(execute: boolean, id: number): void {
  // Record the view of the article
  useEffect(() => {
    void recordView(execute, id)
  }, [execute, id])
}

/**
 * Record the view of the article on the server
 *
 * @param execute - A boolean value to determine if the record view should be executed
 * @param id - The id of the article
 */
export async function recordView(execute: boolean, id: number): Promise<void> {
  // If the record view should not be executed or the code is not running on the client side, return
  if (!execute || !IS_CLIENT) {
    return
  }

  try {
    await fetch(`${process.env.NEXT_PUBLIC_URL}/api/cms-proxy?nid=${id}`, {
      method: 'POST',
    })
  } catch (err) {
    console.error('Failed to record view:', err)
  }
}
