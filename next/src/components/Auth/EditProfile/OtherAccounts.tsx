const OtherAccounts = () => {
  return (
    <>
      <div className="flow-root border-0 border-solid">
        <h3 className="m-0 border-0 border-solid text-xl font-bold leading-7">
          Other accounts
        </h3>
        <ul className="mx-0 mb-6 mt-0 border-0 border-solid p-0">
          <li className="py-4 text-left">
            <div className="flex justify-between border-0 border-solid sm:flex xl:block">
              <div className="flex border-0 border-solid sm:mb-0 xl:mb-4">
                <div className="border-0 border-solid">
                  <img
                    className="block h-6 w-6 max-w-full rounded-full border-0 border-solid align-middle"
                    src="https://flowbite.com/application-ui/demo/images/users/bonnie-green.png"
                    alt="Bonnie image"
                  />
                </div>
                <div className="ml-4 mr-0 min-w-0 flex-1 border-0 border-solid">
                  <p className="mx-0 mb-px mt-0 truncate border-0 border-solid text-base font-semibold leading-none text-gray-900">
                    <PERSON>
                  </p>
                  <p className="mx-0 mb-1 mt-0 truncate border-0 border-solid text-sm font-normal leading-5 text-blue-700">
                    New York, USA
                  </p>
                  <p className="m-0 border-0 border-solid text-xs font-medium leading-4 text-gray-500">
                    Last seen: 1 min ago
                  </p>
                </div>
              </div>
              <div className="inline-flex w-auto items-center border-0 border-solid sm:mx-0 sm:w-auto xl:w-full">
                <a
                  href="#"
                  className="w-full cursor-pointer rounded-lg border border-solid border-gray-300 px-3 py-2 text-center text-sm font-medium leading-5 text-gray-900"
                >
                  Disconnect
                </a>
              </div>
            </div>
          </li>
          <li className="border-x-0 border-b-0 border-t border-solid border-gray-200 py-4 text-left">
            <div className="flex justify-between border-0 border-solid sm:flex xl:block">
              <div className="flex border-0 border-solid sm:mb-0 xl:mb-4">
                <div className="border-0 border-solid">
                  <img
                    className="block h-6 w-6 max-w-full rounded-full border-0 border-solid align-middle"
                    src="https://flowbite.com/application-ui/demo/images/users/jese-leos.png"
                    alt="Jese image"
                  />
                </div>
                <div className="ml-4 mr-0 min-w-0 flex-1 border-0 border-solid">
                  <p className="mx-0 mb-px mt-0 truncate border-0 border-solid text-base font-semibold leading-none text-gray-900">
                    Jese Leos
                  </p>
                  <p className="mx-0 mb-1 mt-0 truncate border-0 border-solid text-sm font-normal leading-5 text-blue-700">
                    California, USA
                  </p>
                  <p className="m-0 border-0 border-solid text-xs font-medium leading-4 text-gray-500">
                    Last seen: 2 min ago
                  </p>
                </div>
              </div>
              <div className="inline-flex w-auto items-center border-0 border-solid sm:mx-0 sm:w-auto xl:w-full">
                <a
                  href="#"
                  className="w-full cursor-pointer rounded-lg border border-solid border-gray-300 px-3 py-2 text-center text-sm font-medium leading-5 text-gray-900"
                >
                  Disconnect
                </a>
              </div>
            </div>
          </li>
          <li className="border-x-0 border-b-0 border-t border-solid border-gray-200 py-4 text-left">
            <div className="flex justify-between border-0 border-solid sm:flex xl:block">
              <div className="flex border-0 border-solid sm:mb-0 xl:mb-4">
                <div className="border-0 border-solid">
                  <img
                    className="block h-6 w-6 max-w-full rounded-full border-0 border-solid align-middle"
                    src="https://flowbite.com/application-ui/demo/images/users/thomas-lean.png"
                    alt="Thomas image"
                  />
                </div>
                <div className="ml-4 mr-0 min-w-0 flex-1 border-0 border-solid">
                  <p className="mx-0 mb-px mt-0 truncate border-0 border-solid text-base font-semibold leading-none text-gray-900">
                    Thomas Lean
                  </p>
                  <p className="mx-0 mb-1 mt-0 truncate border-0 border-solid text-sm font-normal leading-5 text-blue-700">
                    Texas, USA
                  </p>
                  <p className="m-0 border-0 border-solid text-xs font-medium leading-4 text-gray-500">
                    Last seen: 1 hour ago
                  </p>
                </div>
              </div>
              <div className="inline-flex w-auto items-center border-0 border-solid sm:mx-0 sm:w-auto xl:w-full">
                <a
                  href="#"
                  className="w-full cursor-pointer rounded-lg border border-solid border-gray-300 px-3 py-2 text-center text-sm font-medium leading-5 text-gray-900"
                >
                  Disconnect
                </a>
              </div>
            </div>
          </li>
          <li className="border-x-0 border-b-0 border-t border-solid border-gray-200 pt-4 text-left">
            <div className="flex justify-between border-0 border-solid sm:flex xl:block">
              <div className="flex border-0 border-solid sm:mb-0 xl:mb-4">
                <div className="border-0 border-solid">
                  <img
                    className="block h-6 w-6 max-w-full rounded-full border-0 border-solid align-middle"
                    src="https://flowbite.com/application-ui/demo/images/users/lana-byrd.png"
                    alt="Lana image"
                  />
                </div>
                <div className="ml-4 mr-0 min-w-0 flex-1 border-0 border-solid">
                  <p className="mx-0 mb-px mt-0 truncate border-0 border-solid text-base font-semibold leading-none text-gray-900">
                    Lana Byrd
                  </p>
                  <p className="mx-0 mb-1 mt-0 truncate border-0 border-solid text-sm font-normal leading-5 text-blue-700">
                    Texas, USA
                  </p>
                  <p className="m-0 border-0 border-solid text-xs font-medium leading-4 text-gray-500">
                    Last seen: 1 hour ago
                  </p>
                </div>
              </div>
              <div className="inline-flex w-auto items-center border-0 border-solid sm:mx-0 sm:w-auto xl:w-full">
                <a
                  href="#"
                  className="w-full cursor-pointer rounded-lg border border-solid border-gray-300 px-3 py-2 text-center text-sm font-medium leading-5 text-gray-900"
                >
                  Disconnect
                </a>
              </div>
            </div>
          </li>
        </ul>
        <div className="border-0 border-solid">
          <button className="m-0 cursor-pointer rounded-lg border-0 border-solid bg-blue-700 bg-none px-5 py-2 text-center text-sm font-medium normal-case leading-5 text-white">
            Save all
          </button>
        </div>
      </div>
    </>
  )
}

export default OtherAccounts
