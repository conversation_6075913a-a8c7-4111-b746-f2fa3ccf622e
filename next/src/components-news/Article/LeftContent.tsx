import type { FC } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import type { ArticleProps } from '~/src/components-news/Article/ArticleProps'
import { AudioPlayer } from '~/src/components-news/Article/AudioPlayer'
import { AuthorBlock } from '~/src/components-news/Article/AuthorBlock'
import { SourceBlock } from '~/src/components-news/Article/SourceBlock'
import { Spacer } from '~/src/components/spacer/spacer.component'

export const LeftContent: FC<ArticleProps> = ({ articleData, counter }) => {
  const adCounter = counter === -1 ? 1 : counter + 2

  return (
    <div className="md:w-[190px]">
      <AuthorBlock
        authorData={articleData?.author}
        publishDate={articleData?.createdAt}
        updateDate={articleData?.updatedAt}
        supportingAuthors={articleData?.supportingAuthors ?? []}
      />
      {!!articleData?.source && (
        <>
          <Spacer className="h-[30px]" />
          <SourceBlock
            name={articleData?.source?.name}
            description={articleData?.source?.description}
            subtitle={articleData?.source?.subtitle}
          />
        </>
      )}
      <div className="mt-[30px] flex md:mt-0 md:block">
        <AudioPlayer articleData={articleData} />
        <Spacer className="hidden h-[30px] md:block" />
      </div>
      <Spacer className="h-[30px]" />
      <AdvertisingSlot
        id={`left-rail-${adCounter}-news`}
        className={
          'mx-auto hidden min-h-[600px] w-[160px] md:block sticky top-[100px]'
        }
      />
    </div>
  )
}
