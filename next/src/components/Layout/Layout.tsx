import { clsx } from 'clsx'
import { useRouter } from 'next/router'
import type React from 'react'
import { useEffect, useRef, useState } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import { ErrBoundary } from '../ErrBoundary/ErrBoundary'
import Footer from '../Footer/Footer'
import Header from '../Header/Header'
import NavVer2 from '../NavVer2/NavVer2'
import styles from './Layout.module.scss'

interface Props {
  children?: React.ReactNode
  title: string
}

const PATHNAME_NO_PRINT = '/price/precious-metals/text-quotes'

const Layout = ({ children, title }: Props) => {
  const router = useRouter()
  const [hidden, setHidden] = useState(null)
  const [isMobileMenu, setIsMobileMenu] = useState(false)
  const heightAds = useRef(null)
  const heightNav = useRef(null)
  const navPositionY = heightNav.current?.getBoundingClientRect().y
  const [windowH, setWindowH] = useState(0)

  useEffect(() => {
    const updateHeight = () => {
      if (heightNav.current) {
        setWindowH(window.innerHeight - navPositionY)
      }
    }

    updateHeight()

    window.addEventListener('resize', updateHeight)
    return () => {
      window.removeEventListener('resize', updateHeight)
    }
  }, [heightNav.current, isMobileMenu])

  return (
    <>
      <Header title={title} />
      <div className="py-5 bg-white no-print" ref={heightAds}>
        <AdvertisingSlot
          id={'leaderboard'}
          className={clsx(
            'h-[100px] w-[320px] md:h-[90px] md:w-[728px] lg:w-[970px] lg:h-[250px]',
            'mx-auto',
            'flex items-center justify-center no-print',
          )}
        />
      </div>
      <div
        ref={heightNav}
        className={clsx(
          'z-[999]',
          isMobileMenu
            ? 'sticky overflow-auto z-[999999999] bg-black/50'
            : 'bg-white',
          hidden || hidden === null ? '' : 'sticky top-0',
          router.pathname === PATHNAME_NO_PRINT ? 'no-print' : '',
        )}
        style={{ height: isMobileMenu ? `${`${windowH}px`}` : 'auto' }}
      >
        <NavVer2
          onShowHide={setHidden}
          onMenuMobile={setIsMobileMenu}
          headerRef={{
            heightHeader: Number(
              heightAds.current?.clientHeight + heightNav.current?.clientHeight,
            ),
            heightAds: Number(heightAds.current?.clientHeight),
          }}
        />
      </div>
      <div
        className={router.pathname === PATHNAME_NO_PRINT ? 'no-print' : ''}
        style={{ position: 'relative', height: '20px' }}
      />
      <main className={styles.mainAppWrapper}>
        <ErrBoundary>{children}</ErrBoundary>
      </main>
      <div className={router.pathname === PATHNAME_NO_PRINT ? 'no-print' : ''}>
        <Footer />
      </div>
    </>
  )
}

export default Layout
