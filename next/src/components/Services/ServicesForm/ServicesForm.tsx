import { yupResolver } from '@hookform/resolvers/yup'
import { useRouter } from 'next/router'
import type { FC } from 'react'
import { useForm } from 'react-hook-form'
import * as yup from 'yup'
import InputWithValidation from '~/src/components/Services/InputWithValidation/InputWithValidation'
import PrivacyText from '~/src/components/Services/PrivacyText/PrivacyText'
import cs from '~/src/utils/cs'
import useScreenSize from '~/src/utils/useScreenSize'
import {
  CHANNELS,
  SERVICE_STATUS,
  type ServicesType,
} from '~/src/utils/useSubscriptions'
import styles from './ServicesForm.module.scss'

interface Props {
  services: ServicesType
  setServices: (args: ServicesType) => void
  urlRedirect: any
  completeSub: boolean
  loading: boolean
  setLoading: (...args: any) => void
  setMessage: (...args: any) => void
  handleDiscardSelection: (event: React.MouseEvent<HTMLElement>) => void
  handleStepComplete: (
    event: React.MouseEvent<HTMLElement> | React.KeyboardEvent<HTMLElement>,
  ) => void
}

interface IFormInput {
  name: string
  email: string
}

const schema = yup.object().shape({
  name: yup
    .string()
    .required('Please specify your name.')
    .min(2, 'Please specify your name.'),
  email: yup
    .string()
    .required('We need your email address to contact you.')
    .email('Please enter a valid email address.'),
})

const ServicesForm = ({
  services,
  setServices,
  loading,
  urlRedirect,
  completeSub,
  setLoading,
  setMessage,
  handleDiscardSelection,
  handleStepComplete,
}: Props) => {
  const router = useRouter()
  const { isMobile } = useScreenSize()
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  })

  const servicesID = () => {
    const arrKey = []
    for (const [key, value] of Object.entries(services)) {
      if (value === SERVICE_STATUS.select) arrKey.push(key)
    }

    return arrKey.map((channel) => CHANNELS[channel]).toString()
  }

  const servicesSelected = () => {
    const servicesNew = { ...services }
    for (const [key, value] of Object.entries(servicesNew)) {
      if (value === SERVICE_STATUS.select)
        servicesNew[key] = SERVICE_STATUS.selected
    }

    return servicesNew
  }

  const countSelect = () => {
    const arrChecked = []
    for (const [key, value] of Object.entries(services)) {
      if (value === SERVICE_STATUS.select) arrChecked.push(key)
    }

    return arrChecked.length
  }

  const submitForm = async (data: IFormInput) => {
    const dataSubmit = {
      sourceId: 14,
      channels: servicesID(),
      email: data.email,
      urlSafeEmail: encodeURIComponent(data.email),
      ipAddress: 'IP',
    }

    setLoading(true)

    try {
      const response = await fetch('/api/subscribe/services', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        body: JSON.stringify(dataSubmit),
      })

      if (response.ok) {
        setMessage({
          msg: 'Thank you for subscribing',
          isSuccess: true,
        })
        setServices(servicesSelected())
        localStorage.setItem(
          'KITCO_CHANNELS',
          JSON.stringify(servicesSelected()),
        )
        setTimeout(() => {
          setLoading(false)
        }, 3000)
        if (urlRedirect) await router.push(urlRedirect)
      } else {
        throw new Error('Network response was not ok')
      }
    } catch {
      setLoading(false)
      setMessage({
        msg: 'Server error',
        isSuccess: false,
      })
    }
  }

  if (isMobile && completeSub) {
    return (
      <div>
        <div className={cs([styles.itemSelected, 'text-center lg:hidden'])}>
          {countSelect()} newsletter{countSelect() > 1 ? 's' : ''} selected
        </div>
        <div
          className={cs(['text-center', styles.buttonSignUp])}
          onClick={handleStepComplete}
          onKeyDown={handleStepComplete}
        >
          Complete subscription
        </div>
      </div>
    )
  }

  return (
    <form action="" onSubmit={handleSubmit(submitForm)}>
      {urlRedirect && (
        <div
          className={cs([
            styles.textConfirm,
            'mx-auto w-full text-center lg:w-[75%]',
          ])}
        >
          You are about to navigate to another page, would you like to complete
          your subscription for the selected newsletters before proceeding ?
        </div>
      )}
      <div className="mb-8 flex flex-col gap-7 lg:flex-row lg:items-center">
        <div className="w-full lg:w-7/12">
          {!urlRedirect && (
            <>
              <p className={cs([styles.formLabel, 'hidden lg:block'])}>
                Enter your name and email address to receive the selected
                newsletters.
              </p>
              <div
                className={cs([styles.itemSelected, 'text-center lg:hidden'])}
              >
                {countSelect()} newsletter{countSelect() > 1 ? 's' : ''}{' '}
                selected
              </div>
            </>
          )}
          <div className="flex flex-col gap-x-8 gap-y-2.5 lg:flex-row lg:gap-y-0">
            <InputWithValidation
              name="name"
              placeholder="Name"
              errors={errors}
              register={register}
            />
            <InputWithValidation
              name="email"
              placeholder="Email"
              errors={errors}
              register={register}
            />
          </div>
        </div>
        <div
          className={cs([
            'w-full grow text-center lg:w-5/12',
            urlRedirect && styles.noneAuto,
          ])}
        >
          <ButtonActionForm
            loading={loading}
            url={urlRedirect}
            count={countSelect()}
            handleDiscard={handleDiscardSelection}
          />
        </div>
      </div>
      <PrivacyText />
    </form>
  )
}

export default ServicesForm

const ButtonActionForm: FC<{
  loading?: boolean
  url: any
  count: number
  handleDiscard: (
    event: React.MouseEvent<HTMLElement> | React.KeyboardEvent<HTMLElement>,
  ) => void
}> = ({ url, count, handleDiscard, loading }) => {
  return (
    <>
      {!url && (
        <div className={cs([styles.itemSelected, 'hidden lg:block'])}>
          {count} newsletter{count > 1 ? 's' : ''} selected
        </div>
      )}
      <button
        type="submit"
        disabled={loading}
        className={cs([
          styles.buttonSignUp,
          loading && '!cursor-not-allowed opacity-60',
        ])}
      >
        {!loading ? 'Sign Up' : 'Submitting...'}
      </button>
      {url && (
        <div className={styles.stepConfirm}>
          <span>OR</span>
          <div
            className={styles.btnDiscard}
            onClick={handleDiscard}
            onKeyDown={handleDiscard}
          >
            Discard Selection
          </div>
        </div>
      )}
    </>
  )
}
