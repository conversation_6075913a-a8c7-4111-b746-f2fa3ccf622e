import clsx from 'clsx'
import type { FC } from 'react'
import BarchartChartGrid from '~/src/components-markets/BarchartChartGrid/BarchartChartGrid'

interface Props {
  columns: 1 | 2 | 3
}

const SkeletonChart: FC<Props> = ({ columns }) => {
  const renderSkeletonDivs = () => {
    const divs = []
    for (let i = 0; i < columns; i++) {
      divs.push(
        <div key={i}>
          <div className={clsx('animate-loading h-[24px] w-[280px]')} />
          <div
            className={
              'animate-loading h-[280px] w-[280px] rounded-md !bg-black/10'
            }
          />
        </div>,
      )
    }
    return divs
  }

  return (
    <BarchartChartGrid columns={columns}>
      {renderSkeletonDivs()}
    </BarchartChartGrid>
  )
}

export default SkeletonChart
