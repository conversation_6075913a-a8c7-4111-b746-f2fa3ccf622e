import Link from 'next/link'
import type { FC } from 'react'
import { CategoryLink } from '~/src/components-news/ArticleTeasers/CategoryLink'
import { DateStamp } from '~/src/components-news/ArticleTeasers/DateStamp'
import type { TeaserCardProps } from '~/src/components-news/ArticleTeasers/TeaserCard'
import cs from '~/src/utils/cs'

export const TeaserLabelTitleDate: FC<TeaserCardProps> = ({ node, size }) => {
  const sizeCSS = {
    sm: 'text-[14px] leading-[142%]',
    md: 'text-[16px] leading-[130%]',
    lg: 'text-[24px] leading-[130%]',
    xl: 'text-[24px] leading-[130%]',
  }
  return (
    <div className="w-full">
      <>
        <CategoryLink
          urlAlias={node?.category?.urlAlias}
          text={node?.category?.name}
        />
        <div className="h-1 bg-transparent" />
      </>
      <Link href={node?.urlAlias ?? '/'}>
        <>
          <h3 className={cs([sizeCSS[size], 'line-clamp-2'])}>
            {node?.teaserHeadline ?? node?.title}
          </h3>
          <div className="h-2 bg-transparent" />
          <DateStamp stamp={node?.createdAt} />
        </>
      </Link>
    </div>
  )
}
