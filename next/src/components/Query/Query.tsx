import type { UseQueryOptions, UseQueryResult } from '@tanstack/react-query'
import type React from 'react'
import type { ReactNode } from 'react'
import kitcoQuery from '~/src/services/database/kitcoQuery'

interface QueryProps<TData, TError = unknown> {
  fetcher: UseQueryOptions<TData, TError>
  children: (queryResult: UseQueryResult<TData, TError>) => React.ReactNode
}

export const Query = <TData, TError = unknown>({
  fetcher,
  children,
}: QueryProps<TData, TError>): ReactNode => {
  const queryResult = kitcoQuery(fetcher)

  return <>{children(queryResult)}</>
}
