import type { UseQueryOptions } from '@tanstack/react-query'
import { gql } from 'graphql-request'

import type {
  CommoditiesNewsSidebarLatestNewsQuery,
  CommoditiesNewsSidebarLatestNewsQueryVariables,
  CommoditiesNewsSidebarTrendingQuery,
  CommoditiesNewsSidebarTrendingQueryVariables,
} from '~/src/generated'
import { commoditiesNewsSidebarArticleTeaserFragment } from '~/src/lib/Commodities/Fragments'
import { graphs } from '~/src/services/database/fetcher'
import type QueryArgs from '~/src/types/QueryArgs'

export const CommoditiesNewsSidebarQueries = {
  commoditiesNewsSidebarLatestNews: (
    args: QueryArgs<
      CommoditiesNewsSidebarLatestNewsQueryVariables,
      CommoditiesNewsSidebarLatestNewsQuery
    >,
  ): UseQueryOptions<CommoditiesNewsSidebarLatestNewsQuery> => {
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['commoditiesNewsSidebarLatestNews', args.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            ${commoditiesNewsSidebarArticleTeaserFragment}
            query CommoditiesNewsSidebarLatestNews(
              $urlAlias: String!
              $limit: Int
              $offset: Int
              $includeRelatedCategories: Boolean
              $includeEntityQueues: Boolean
            ) {
              nodeListByCategory(
                limit: $limit
                offset: $offset
                urlAlias: $urlAlias
                includeRelatedCategories: $includeRelatedCategories
                includeEntityQueues: $includeEntityQueues
              ) {
                total
                items {
                  ... on NewsArticle {
                    ...CommoditiesNewsSidebarArticleTeaserFragment
                  }
                  ... on Commentary {
                    id
                    category {
                      ...CommoditiesNewsSidebarCategoryFragment
                    }
                    teaserSnippet
                    title
                    urlAlias
                    createdAt
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  commoditiesNewsSidebarTrending: (
    args?: QueryArgs<
      CommoditiesNewsSidebarTrendingQueryVariables,
      CommoditiesNewsSidebarTrendingQuery
    >,
  ): UseQueryOptions<CommoditiesNewsSidebarTrendingQuery> => {
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['commoditiesNewsSidebarTrending', args?.variables?.limit],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            ${commoditiesNewsSidebarArticleTeaserFragment}
            query CommoditiesNewsSidebarTrending($limit: Int) {
              nodeListTrending(
                bundles: [NewsArticle]
                sort: Week
                limit: $limit
              ) {
                ... on NewsArticle {
                  ...CommoditiesNewsSidebarArticleTeaserFragment
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },
}
