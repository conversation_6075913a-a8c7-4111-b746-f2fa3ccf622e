import Link from 'next/link'
import * as Navigation from './../Composables'
import BaseMetalsMenu from './BaseMetalsMenu'

interface Props {
  value: string
  onNodeUpdate?: any
}

const BaseMetalsItem = ({ value, onNodeUpdate }: Props) => {
  return (
    <Navigation.Item value={value}>
      <Navigation.Trigger value={value} onNodeUpdate={onNodeUpdate}>
        <Link
          href="/price/base-metals"
          className="whitespace-nowrap text-sm font-bold leading-5 text-white"
        >
          <span className="group-hover:underline">{value}</span>
        </Link>
      </Navigation.Trigger>
      <Navigation.Content>
        <BaseMetalsMenu />
      </Navigation.Content>
    </Navigation.Item>
  )
}

export default BaseMetalsItem
