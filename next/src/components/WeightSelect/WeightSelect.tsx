import {
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
  Transition,
} from '@headlessui/react'
import clsx from 'clsx'
import { type FC, Fragment, useEffect, useMemo } from 'react'
import { HiCheck } from 'react-icons/hi'
import { IoChevronDownSharp } from 'react-icons/io5'
import { useDispatch, useSelector } from 'react-redux'
import type { AppDispatch, RootState } from '~/src/features/store/store'
import { initializeWeight, setWeight } from '~/src/features/store/weightSlice'
import { useListboxWidthSync } from '~/src/hooks/useListboxWidthSync'
import type { WeightHashEntry } from '~/src/types/WeightSelect/WeightHash'
import type WeightType from '~/src/types/WeightSelect/WeightType'

/**
 * WeightSelect props
 *
 * @param {WeightType} type - The weight type
 * @param {string} defaultWeight - The default weight value
 * @param {string} id - The id of the weight select
 * @param {string} classNamesListbox - The class names for the listbox
 */
export interface WeightSelectProps {
  type: WeightType
  defaultWeight?: string
  id?: string
  classNamesListbox?: string
}

export const WeightSelect: FC<WeightSelectProps> = ({
  type,
  defaultWeight,
  id = 'global',
  classNamesListbox,
}: WeightSelectProps) => {
  // Use the dispatch to update the weight state
  const dispatch = useDispatch<AppDispatch>()

  // Use the useListboxWidthSync hook to set the width of the listbox
  const { buttonRef, buttonWidth } = useListboxWidthSync<HTMLDivElement>()

  // Get the selected weight value from the state
  const weight = useSelector(
    (state: RootState) => state.weight[id].selectedWeight,
  )

  // Get the weight hash from the state
  const weightHash = useSelector(
    (state: RootState) => state.weight[id].weightHash,
  )

  // Initialize the weight state when the component is mounted
  useEffect(() => {
    dispatch(initializeWeight({ type, defaultWeight, id }))
  }, [dispatch, defaultWeight, type, id])

  /**
   * Handle the WeightChange event
   *
   * @param {string} label - The selected weight label
   */
  const handleWeightChange = (label: string) => {
    dispatch(setWeight({ type, label, id }))
  }

  // Get the selected weight label from the state
  const readLabel = useMemo(
    () => (weight?.label ? weight.label.toString() : null),
    [weight],
  )

  if (!weight) return null

  return (
    <>
      <div className="relative">
        <Listbox value={readLabel ?? 'Select'} onChange={handleWeightChange}>
          <div className={clsx('relative', classNamesListbox)} ref={buttonRef}>
            <ListboxButton
              className={clsx(
                'h-7 px-2',
                'flex items-center gap-1',
                'rounded-sm border border-ktc-gray/20',
              )}
            >
              <span className="block truncate">{readLabel}</span>
              <span className="pl-1">
                <IoChevronDownSharp />
              </span>
            </ListboxButton>
            <Transition
              as={Fragment}
              leave="transition ease-in duration-100"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <ListboxOptions
                style={{ width: buttonWidth }}
                anchor="bottom end"
                className={clsx(
                  'absolute z-[2000] mt-1 h-auto py-1',
                  'w-auto min-w-[105px] rounded-md bg-white',
                  'text-base shadow-lg ring-1 ring-black/20 focus:outline-none sm:text-sm',
                )}
              >
                {weightHash &&
                  Object.values(weightHash).map(
                    (weightValue: WeightHashEntry) => (
                      <ListboxOption
                        key={weightValue.label}
                        className={clsx(
                          'relative block cursor-pointer select-none px-4 py-2',
                          'data-[focus]:bg-blue-200/30 data-[focus]:text-blue-800 text-gray-900',
                        )}
                        value={weightValue.label}
                      >
                        <div className="flex flex-nowrap items-center gap-2">
                          <span className="block h-4 w-5">
                            {weight.label === weightValue.label && <HiCheck />}
                          </span>
                          <span
                            className={clsx('block basis-12 font-semibold')}
                          >
                            {weightValue.label}
                          </span>
                        </div>
                      </ListboxOption>
                    ),
                  )}
              </ListboxOptions>
            </Transition>
          </div>
        </Listbox>
      </div>
    </>
  )
}
