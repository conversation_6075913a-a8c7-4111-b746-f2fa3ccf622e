import * as VideoTeaser from '~/src/components/VideoTeaser/VideoTeaser'

// import type { VideoResponse } from "~/src/lib/vcms-factory.lib";

export const VideoPlaylistTeaser: React.FC<{
  node: any
  isFetching?: boolean
}> = ({ node, isFetching }) => {
  return (
    <VideoTeaser.CtxProvider node={node} isFetching={isFetching} key={node.id}>
      <div className="flex flex-col gap-1">
        <VideoTeaser.A className="aspect-video">
          <VideoTeaser.Img height={132} width={240} className="block" />
          <VideoTeaser.PlayButton />
        </VideoTeaser.A>
        <div className="block">
          <VideoTeaser.A>
            <VideoTeaser.Title className="py-[5px] !text-base !text-white" />
            <VideoTeaser.Timestamp className="text-white" />
          </VideoTeaser.A>
        </div>
      </div>
    </VideoTeaser.CtxProvider>
  )
}
