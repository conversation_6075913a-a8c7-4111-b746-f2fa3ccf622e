import type { FC } from 'react'

type CategoryIconProps = {
  categoryKey: string
  categoryName: string
  className?: string
}

const CategoryIcon: FC<CategoryIconProps> = ({ 
  categoryKey, 
  categoryName, 
  className = '' 
}) => {
  const getIconByCategory = (category: string) => {
    switch (category) {
      case 'PRECIOUS METALS':
        return (
          <img
            src="/icons/KGX-Ingots.svg"
            alt="Precious Metals icon"
            width={18}
            height={18}
          />
        )
      case 'BASE METALS':
        return (
          <img
            src="/icons/KGX-Anvil.svg"
            alt="Base Metals icon"
            width={20}
            height={20}
          />
        )
      case 'ENERGY':
        return (
          <img 
            src="/icons/KGX-Oil.svg" 
            alt="Energy icon" 
            width={16} 
            height={16} 
          />
        )
      case 'CRYPTOCURRENCIES':
        return (
          <img
            src="/icons/KGX-Crypto.svg"
            alt="Cryptocurrencies icon"
            width={13}
            height={13}
          />
        )
      default:
        return null
    }
  }

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      <div className="flex items-center justify-center w-6 h-6">
        {getIconByCategory(categoryKey)}
      </div>
      <div className="font-['Verdana'] text-lg font-bold leading-none text-neutral-900">
        {categoryName}
      </div>
    </div>
  )
}

export default CategoryIcon
