import type ChangeData from '~/src/types/DataTable/ChangeData'

type BaseCommodityData = {
  commodity: string
  lastBid: {
    bid: string
    bidVal: number
    currency: string
    originalTime: string
  }
  changeDueToUSD: ChangeData
  changeDueToTrade: ChangeData
  totalChange: ChangeData
  type?: string // Optional in base type
}

type KGXCommodityData = BaseCommodityData & {
  type: 'commodity' | 'cryptoCommodity' | 'cryptoLink' | 'learnMore'
  getWidgetMessage: () => {
    text: string
    value: string
    color?: string
    isPositive?: boolean
    isZero?: boolean
    isSpecialLink?: boolean
  }
}

type CategoryHeaderData = {
  commodity: string
  type: 'categoryHeader'
  categoryName: string
  categoryKey: string
  lastBid: {
    bid: string
    bidVal: number
    currency: string
    originalTime: string
  }
  changeDueToUSD: ChangeData
  changeDueToTrade: ChangeData
  totalChange: ChangeData
}

type CommodityData = BaseCommodityData | KGXCommodityData | CategoryHeaderData

export type { KGXCommodityData, CategoryHeaderData }
export default CommodityData
